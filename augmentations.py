#!/usr/bin/env python3
"""
数据增强模块 - 强力正则化与特征扰动
实现SpecAugment和ECG信号增强，对抗模型偏见和欠拟合
"""

import torch
import torch.nn as nn
import numpy as np
import random
from typing import Tuple, Optional


class SpecAugment(nn.Module):
    """
    SpecAugment for PCG spectrograms
    强制模型不能只看固定的频率带或时间段，必须综合全局信息
    """
    def __init__(self, 
                 freq_mask_param: int = 20,  # 频率遮挡参数 (约15%的频率轴)
                 time_mask_param: int = 25,  # 时间遮挡参数 (约20%的时间轴)
                 num_freq_masks: int = 1,    # 频率遮挡数量
                 num_time_masks: int = 1,    # 时间遮挡数量
                 p: float = 0.6):            # 应用概率
        super().__init__()
        self.freq_mask_param = freq_mask_param
        self.time_mask_param = time_mask_param
        self.num_freq_masks = num_freq_masks
        self.num_time_masks = num_time_masks
        self.p = p
        
    def forward(self, spectrogram: torch.Tensor) -> torch.Tensor:
        """
        Args:
            spectrogram: [batch, channels, freq, time] 或 [batch, freq, time]
        Returns:
            augmented spectrogram
        """
        if not self.training or random.random() > self.p:
            return spectrogram
            
        # 确保是4D张量 [batch, channels, freq, time]
        if spectrogram.dim() == 3:
            spectrogram = spectrogram.unsqueeze(1)
            squeeze_later = True
        else:
            squeeze_later = False
            
        batch_size, channels, freq_size, time_size = spectrogram.shape
        augmented = spectrogram.clone()
        
        for batch_idx in range(batch_size):
            # 频率遮挡
            for _ in range(self.num_freq_masks):
                freq_mask_size = random.randint(0, min(self.freq_mask_param, freq_size))
                if freq_mask_size > 0:
                    freq_start = random.randint(0, freq_size - freq_mask_size)
                    augmented[batch_idx, :, freq_start:freq_start + freq_mask_size, :] = 0
            
            # 时间遮挡
            for _ in range(self.num_time_masks):
                time_mask_size = random.randint(0, min(self.time_mask_param, time_size))
                if time_mask_size > 0:
                    time_start = random.randint(0, time_size - time_mask_size)
                    augmented[batch_idx, :, :, time_start:time_start + time_mask_size] = 0
        
        if squeeze_later:
            augmented = augmented.squeeze(1)
            
        return augmented


class ECGAugment(nn.Module):
    """
    ECG信号增强：添加噪声和随机偏移
    让模型关注心跳波形本身，而不是噪声或绝对位置
    """
    def __init__(self,
                 add_noise_p: float = 0.5,           # 添加噪声概率
                 time_shift_p: float = 0.5,          # 时间偏移概率
                 snr_db_range: Tuple[float, float] = (15, 30),  # 信噪比范围
                 shift_limit: float = 0.05):         # 最大偏移比例
        super().__init__()
        self.add_noise_p = add_noise_p
        self.time_shift_p = time_shift_p
        self.snr_db_range = snr_db_range
        self.shift_limit = shift_limit
        
    def add_gaussian_noise(self, signal: torch.Tensor, snr_db: float) -> torch.Tensor:
        """添加高斯噪声"""
        # 计算信号功率
        signal_power = torch.mean(signal ** 2, dim=-1, keepdim=True)
        
        # 根据SNR计算噪声功率
        snr_linear = 10 ** (snr_db / 10)
        noise_power = signal_power / snr_linear
        
        # 生成噪声
        noise = torch.randn_like(signal) * torch.sqrt(noise_power)
        
        return signal + noise
    
    def random_time_shift(self, signal: torch.Tensor) -> torch.Tensor:
        """随机时间偏移"""
        batch_size, channels, length = signal.shape
        shifted_signals = []
        
        for i in range(batch_size):
            # 随机偏移量
            max_shift = int(length * self.shift_limit)
            shift = random.randint(-max_shift, max_shift)
            
            if shift == 0:
                shifted_signals.append(signal[i])
            elif shift > 0:
                # 右移：前面补零，后面截断
                padded = torch.cat([torch.zeros(channels, shift, device=signal.device), 
                                  signal[i, :, :-shift]], dim=1)
                shifted_signals.append(padded)
            else:
                # 左移：后面补零，前面截断
                padded = torch.cat([signal[i, :, -shift:], 
                                  torch.zeros(channels, -shift, device=signal.device)], dim=1)
                shifted_signals.append(padded)
        
        return torch.stack(shifted_signals)
    
    def forward(self, signal: torch.Tensor) -> torch.Tensor:
        """
        Args:
            signal: [batch, channels, length] ECG信号
        Returns:
            augmented signal
        """
        if not self.training:
            return signal
            
        augmented = signal.clone()
        
        # 添加高斯噪声
        if random.random() < self.add_noise_p:
            snr_db = random.uniform(*self.snr_db_range)
            augmented = self.add_gaussian_noise(augmented, snr_db)
        
        # 随机时间偏移
        if random.random() < self.time_shift_p:
            augmented = self.random_time_shift(augmented)
            
        return augmented


class MultimodalAugment(nn.Module):
    """
    多模态数据增强组合
    同时对ECG和PCG应用不同的增强策略
    """
    def __init__(self,
                 # SpecAugment参数
                 freq_mask_param: int = 20,
                 time_mask_param: int = 25,
                 spec_aug_p: float = 0.6,
                 # ECG增强参数
                 ecg_noise_p: float = 0.5,
                 ecg_shift_p: float = 0.5,
                 snr_db_range: Tuple[float, float] = (15, 30),
                 shift_limit: float = 0.05):
        super().__init__()
        
        self.spec_augment = SpecAugment(
            freq_mask_param=freq_mask_param,
            time_mask_param=time_mask_param,
            p=spec_aug_p
        )
        
        self.ecg_augment = ECGAugment(
            add_noise_p=ecg_noise_p,
            time_shift_p=ecg_shift_p,
            snr_db_range=snr_db_range,
            shift_limit=shift_limit
        )
        
    def forward(self, ecg: torch.Tensor, pcg: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Args:
            ecg: [batch, channels, length] ECG信号
            pcg: [batch, channels, freq, time] PCG频谱图
        Returns:
            (augmented_ecg, augmented_pcg)
        """
        augmented_ecg = self.ecg_augment(ecg)
        augmented_pcg = self.spec_augment(pcg)
        
        return augmented_ecg, augmented_pcg


def test_augmentations():
    """测试增强函数"""
    print("🧪 测试数据增强...")
    
    # 创建测试数据
    batch_size = 4
    ecg = torch.randn(batch_size, 1, 16000)  # ECG信号
    pcg = torch.randn(batch_size, 1, 128, 126)  # PCG频谱图
    
    # 创建增强器
    augmenter = MultimodalAugment()
    augmenter.train()  # 设置为训练模式
    
    # 应用增强
    aug_ecg, aug_pcg = augmenter(ecg, pcg)
    
    print(f"✅ ECG: {ecg.shape} -> {aug_ecg.shape}")
    print(f"✅ PCG: {pcg.shape} -> {aug_pcg.shape}")
    print(f"✅ ECG变化: {torch.mean(torch.abs(ecg - aug_ecg)).item():.6f}")
    print(f"✅ PCG变化: {torch.mean(torch.abs(pcg - aug_pcg)).item():.6f}")
    
    print("🎯 数据增强测试完成！")


if __name__ == "__main__":
    test_augmentations()

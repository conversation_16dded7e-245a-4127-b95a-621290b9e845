#!/usr/bin/env python3
"""
简单的多模态训练器
专门用于ECG-PCG多模态网络训练
"""

import torch
import torch.nn as nn
import torch.nn.functional as F  # 添加这个导入修复Focal Loss错误
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import random  # 🔥 添加random导入用于Mixup
from tqdm import tqdm
import os
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, confusion_matrix, roc_curve, auc, roc_auc_score
import config
import datetime
from torch.utils.tensorboard import SummaryWriter
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import seaborn as sns
from sklearn.metrics import confusion_matrix
import itertools
from augmentations import MultimodalAugment  # 🔧 导入数据增强模块

class MultimodalTrainer:
    def __init__(self, model, train_dataset, val_dataset=None, device='cuda', batch_size=8, learning_rate=0.001, num_epochs=50, val_split=0.2, enable_tensorboard=False, enable_augmentation=True):
        self.model = model
        self.train_dataset = train_dataset
        self.val_dataset = val_dataset
        self.device = device
        self.enable_tensorboard = enable_tensorboard  # 🔧 添加TensorBoard开关
        self.enable_augmentation = enable_augmentation  # 🔧 添加数据增强开关

        # 🔧 确保模型在正确的设备上
        self.model = self.model.to(self.device)
        self.batch_size = batch_size
        self.learning_rate = learning_rate
        self.num_epochs = num_epochs
        self.val_split = val_split

        # 如果没有提供验证集，从训练集中分割
        if self.val_dataset is None and self.val_split > 0:
            # 🔧 使用索引分割而不是random_split，避免数据格式问题
            import numpy as np
            total_size = len(train_dataset)
            val_size = int(total_size * val_split)

            # 创建随机索引
            np.random.seed(42)
            indices = np.random.permutation(total_size)
            train_indices = indices[val_size:]
            val_indices = indices[:val_size]

            # 创建子集
            self.train_dataset = torch.utils.data.Subset(train_dataset, train_indices)
            self.val_dataset = torch.utils.data.Subset(train_dataset, val_indices)

            print(f"数据分割完成: 训练集 {len(train_indices)} 样本, 验证集 {len(val_indices)} 样本")

        # 创建results目录和日志文件
        self.results_dir = "results"
        os.makedirs(self.results_dir, exist_ok=True)

        # 创建带时间戳的日志文件
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_file = os.path.join(self.results_dir, f"training_log_multimodal_{timestamp}.txt")

        # 🔧 条件性创建TensorBoard writer
        if self.enable_tensorboard:
            self.tensorboard_dir = os.path.join(self.results_dir, f"tensorboard_multimodal_{timestamp}")
            self.writer = SummaryWriter(log_dir=self.tensorboard_dir)
        else:
            self.tensorboard_dir = None
            self.writer = None

        # 初始化日志文件
        self.log_and_print("="*80)
        self.log_and_print("🚀 多模态ECG-PCG网络训练日志")
        self.log_and_print(f"开始时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        if self.enable_tensorboard:
            self.log_and_print(f"TensorBoard日志目录: {self.tensorboard_dir}")
        else:
            self.log_and_print("TensorBoard: 已禁用")
        self.log_and_print("="*80)
        
        # 创建数据加载器
        self.train_dataloader = DataLoader(
            self.train_dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=4,
            pin_memory=True
        )

        if self.val_dataset is not None:
            self.val_dataloader = DataLoader(
                self.val_dataset,
                batch_size=batch_size,
                shuffle=False,
                num_workers=4,
                pin_memory=True
            )
        else:
            self.val_dataloader = None
        
        # 🔥 适中的损失函数权重 - 平衡类别关注度
        # 数据分布: 26.5% 正常, 73.5% 异常 (比例约2.77:1)
        # 🚀 使用适中的权重来平衡类别学习，避免过度偏向少数类
        class_weights = torch.tensor([2.5, 1.0], device=device)  # [正常类, 异常类] - 回到适中的2.5
        self.criterion = nn.CrossEntropyLoss(weight=class_weights)

        # 🔥 备选权重 - 可以根据需要调整
        self.moderate_class_weights = torch.tensor([3.0, 1.0], device=device)
        self.use_moderate_weights = False  # 可以动态切换

        # 🔥 实现差分学习率和预训练层冻结
        self.freeze_pretrained_layers()
        param_groups = self.setup_differential_learning_rates(learning_rate)

        # 🔥 适中的优化器设置 - 差分学习率 + 适度的权重衰减
        self.optimizer = optim.AdamW(
            param_groups,  # 使用参数组而不是model.parameters()
            weight_decay=1e-4,  # 🔬 实验A：标准权重衰减 (0.0001)
            betas=(0.9, 0.999),
            eps=1e-8
        )

        print(f"🔬 实验B配置 - 类别权重: 正常类权重={class_weights[0]:.1f}, 异常类权重={class_weights[1]:.1f}")
        print(f"   🎯 目标: 在稳定基线上优化性能")
        print(f"   🚀 微调学习率 + 标准权重衰减")
        print(f"   ⚡ 准备逐步引入数据增强")

        # 🔬 实验A：禁用阈值优化，使用固定0.5阈值
        self.classification_threshold = 0.5  # 固定阈值
        self.optimal_threshold = 0.5  # 固定阈值
        self.threshold_search_enabled = False  # 🔬 禁用阈值搜索

        # 🔥 适中数据增强 - 平衡正则化和学习能力
        if self.enable_augmentation:
            self.augmenter = MultimodalAugment(
                # 🔬 实验B：温和SpecAugment参数 - 轻微的频谱增强
                freq_mask_param=10,    # 频率遮挡10点 (约8%的频率轴)
                time_mask_param=10,    # 时间遮挡10点 (约8%的时间轴)
                spec_aug_p=0.3,        # 30%的样本应用SpecAugment

                # 🔬 实验B：温和ECG增强参数 - 轻微的噪声和变换
                ecg_noise_p=0.3,       # 30%的样本添加噪声
                ecg_shift_p=0.3,       # 30%的样本时间偏移
                snr_db_range=(20, 30), # 信噪比20-30dB (更高信噪比，更轻微噪声)
                shift_limit=0.03       # 最大偏移3%信号长度
            ).to(device)

            # 🔥 适中Mixup增强 - 平衡的混合策略
            self.mixup_alpha = 0.2  # 适中的alpha=0.2
            self.mixup_prob = 0.4   # 40%的批次应用Mixup

            # 🔥 适中：幅度缩放增强
            self.amplitude_scaling_p = 0.3  # 30%概率进行幅度缩放
            self.amplitude_range = (0.8, 1.2)  # 缩放范围80%-120%

            print(f"🔥 启用适中数据增强 - 平衡正则化和学习:")
            print(f"   📊 SpecAugment: 频率遮挡{20}点, 时间遮挡{20}点, 概率50%")
            print(f"   🫀 ECG增强: 噪声+偏移各50%概率, SNR 15-25dB, 偏移5%")
            print(f"   🎯 Mixup: alpha={self.mixup_alpha}, 概率40% - 平衡策略")
            print(f"   📈 幅度缩放: 概率30%, 范围80%-120%")
            print(f"   🚀 目标: 适度正则化，保持模型学习能力")
        else:
            self.augmenter = None
            self.mixup_alpha = 0.0
            self.mixup_prob = 0.0
            self.amplitude_scaling_p = 0.0
            print(f"🚫 数据增强已禁用 - 保护原始医学信号特征")

        # 🔥 优化Focal Loss作为备选方案 - 当权重增加导致不稳定时使用
        self.use_focal_loss = False  # 默认使用加权交叉熵，Focal Loss作为备选
        self.focal_alpha = 0.8       # 进一步增加对少数类的关注
        self.focal_gamma = 2.0       # 适中的gamma值，平衡困难样本关注
        self.focal_weights = torch.tensor([0.8, 0.2], device=device)  # Focal Loss的类别权重

        print(f"🔥 优化Focal Loss备选方案: alpha={self.focal_alpha}, gamma={self.focal_gamma}")
        print(f"   📊 当前使用: {'Focal Loss' if self.use_focal_loss else 'Weighted CrossEntropy'}")
        print(f"   🔧 Focal权重: {self.focal_weights.cpu().numpy()}")
        print(f"   🎯 备选策略: 当权重增加导致训练不稳定时自动切换")
        print(f"   ⚡ 动态切换: 可根据训练稳定性调整")

        # 🔥 强化学习率调度器 - 短Warmup + 余弦退火
        self.num_warmup_epochs = 5  # 5个epoch的warmup
        self.warmup_steps = self.num_warmup_epochs * len(self.train_dataloader)
        self.total_steps = num_epochs * len(self.train_dataloader)

        # 🔬 实验A：完全禁用学习率调度器，使用固定学习率
        self.scheduler = None  # 禁用所有学习率调度器

        print(f"🔬 实验B - 基线优化配置:")
        print(f"   📊 微调学习率: {learning_rate:.6f}")
        print(f"   ⚡ 权重衰减: 1e-4 (标准正则化)")
        print(f"   🎯 目标: 在稳定基线上提升性能")

        self.current_step = 0
        
        # 训练历史
        self.train_losses = []
        self.train_accuracies = []
        self.val_losses = []
        self.val_accuracies = []
        self.train_precisions = []
        self.train_recalls = []
        self.train_f1s = []
        self.val_precisions = []
        self.val_recalls = []
        self.val_f1s = []

        # 混淆矩阵历史 - 只保留最新和最佳
        self.train_confusion_matrices = []
        self.val_confusion_matrices = []
        self.class_names = ['Normal', 'Abnormal']  # 根据你的数据集调整
        self.best_epoch_cm = None  # 保存最佳epoch的混淆矩阵
        self.best_accuracy = 0.0
        self.best_epoch_num = 0

        # 🔥 强化早停机制 - 监控Macro F1-Score
        self.patience = 20  # 20个epoch没有改善就停止 (增加耐心)
        self.patience_counter = 0
        self.min_delta = 0.001  # 最小改善阈值
        self.best_macro_f1 = 0.0  # 监控Macro F1-Score而不是准确率
        self.best_val_f1 = 0.0    # 最佳验证F1分数
        self.early_stop_triggered = False

        print(f"🔥 启用强化早停机制:")
        print(f"   📊 监控指标: Macro F1-Score (更适合不平衡数据)")
        print(f"   ⏰ 耐心值: {self.patience} epochs")
        print(f"   📈 最小改善: {self.min_delta}")
        print(f"   🎯 目标: 防止训练后期性能下降，节省时间")

        # 决策阈值调整
        self.decision_threshold = 0.5  # 默认阈值
        self.use_threshold_tuning = True  # 启用阈值调整
        print(f"🎯 启用决策阈值调整: 默认阈值={self.decision_threshold}")

        # 初始化实时绘图
        self.setup_realtime_plotting()

    def mixup_data(self, ecg, pcg, labels, alpha=0.2):
        """
        🔥 Mixup数据增强 - 对抗过拟合和类别不平衡的王牌
        """
        if alpha > 0:
            lam = np.random.beta(alpha, alpha)
        else:
            lam = 1

        batch_size = ecg.size(0)
        index = torch.randperm(batch_size).to(ecg.device)

        mixed_ecg = lam * ecg + (1 - lam) * ecg[index, :]
        mixed_pcg = lam * pcg + (1 - lam) * pcg[index, :]
        y_a, y_b = labels, labels[index]

        return mixed_ecg, mixed_pcg, y_a, y_b, lam

    def mixup_criterion(self, outputs, y_a, y_b, lam):
        """
        🔥 Mixup损失函数
        """
        if self.use_focal_loss:
            return lam * self.focal_loss(outputs, y_a) + (1 - lam) * self.focal_loss(outputs, y_b)
        else:
            return lam * self.criterion(outputs, y_a) + (1 - lam) * self.criterion(outputs, y_b)

    def apply_amplitude_scaling(self, ecg, pcg):
        """
        🔥 幅度缩放增强 - 对ECG和PCG信号进行随机幅度缩放
        """
        if random.random() < self.amplitude_scaling_p:
            # 为每个样本生成不同的缩放因子
            batch_size = ecg.size(0)

            # ECG幅度缩放
            ecg_scale = torch.FloatTensor(batch_size, 1, 1).uniform_(
                self.amplitude_range[0], self.amplitude_range[1]
            ).to(ecg.device)
            ecg = ecg * ecg_scale

            # PCG幅度缩放（频谱图）
            pcg_scale = torch.FloatTensor(batch_size, 1, 1, 1).uniform_(
                self.amplitude_range[0], self.amplitude_range[1]
            ).to(pcg.device)
            pcg = pcg * pcg_scale

            return ecg, pcg, True

        return ecg, pcg, False

    def focal_loss(self, outputs, targets):
        """
        🔥 优化Focal Loss实现 - 支持类别权重的不平衡数据处理
        """
        # 计算交叉熵损失
        ce_loss = F.cross_entropy(outputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)

        # 应用类别权重
        if hasattr(self, 'focal_weights') and self.focal_weights is not None:
            # 获取每个样本对应的权重
            weights = self.focal_weights[targets]
            focal_loss = weights * self.focal_alpha * (1 - pt) ** self.focal_gamma * ce_loss
        else:
            # 原始Focal Loss
            focal_loss = self.focal_alpha * (1 - pt) ** self.focal_gamma * ce_loss

        return focal_loss.mean()

    def switch_to_focal_loss(self):
        """
        🔥 动态切换到Focal Loss - 当权重增加导致训练不稳定时使用
        """
        if not self.use_focal_loss:
            self.use_focal_loss = True
            print(f"🔄 切换到Focal Loss: alpha={self.focal_alpha}, gamma={self.focal_gamma}")
            print(f"   🎯 原因: 权重增加可能导致训练不稳定")
            print(f"   📊 期望: 更平滑的类别平衡处理")

    def switch_to_weighted_ce(self):
        """
        🔥 切换回加权交叉熵
        """
        if self.use_focal_loss:
            self.use_focal_loss = False
            print(f"🔄 切换回加权交叉熵: 权重={self.criterion.weight.cpu().numpy()}")
            print(f"   🎯 原因: Focal Loss可能过于复杂")
            print(f"   📊 期望: 更直接的类别平衡")

    def switch_to_extreme_weights(self):
        """
        🔥 切换到极端权重[5.0, 1.0] - 当4.0权重效果不够时
        """
        if not self.use_extreme_weights:
            self.use_extreme_weights = True
            self.criterion = nn.CrossEntropyLoss(weight=self.extreme_class_weights)
            print(f"🔄 切换到极端权重: {self.extreme_class_weights.cpu().numpy()}")
            print(f"   🎯 原因: 当前权重[4.0, 1.0]对少数类关注不够")
            print(f"   📊 期望: 进一步提升特异性")
            print(f"   ⚠️ 注意: 可能导致训练不稳定，需要监控")

    def detect_training_instability(self, recent_losses, threshold=0.1):
        """
        🔥 检测训练不稳定性 - 基于损失波动
        """
        if len(recent_losses) < 5:
            return False

        # 计算最近5个epoch的损失标准差
        loss_std = np.std(recent_losses[-5:])
        loss_mean = np.mean(recent_losses[-5:])

        # 如果标准差相对于均值过大，认为不稳定
        instability_ratio = loss_std / (loss_mean + 1e-8)

        if instability_ratio > threshold:
            print(f"⚠️ 检测到训练不稳定: 损失波动比例={instability_ratio:.3f}")
            return True

        return False

    def update_learning_rate(self, epoch, val_macro_f1=None):
        """
        🔬 实验B：固定学习率，不进行任何调整
        """
        # 实验B：继续使用固定学习率
        current_lr = self.optimizer.param_groups[0]['lr']
        print(f"🔬 固定学习率: {current_lr:.6f} (微调版本)")

    def compute_macro_f1(self, y_true, y_pred):
        """
        🔥 计算Macro F1-Score - 早停机制的监控指标
        """
        from sklearn.metrics import f1_score
        try:
            macro_f1 = f1_score(y_true, y_pred, average='macro', zero_division=0)
            return macro_f1
        except Exception as e:
            print(f"⚠️ 计算Macro F1-Score失败: {e}")
            return 0.0

    def find_optimal_threshold(self, val_loader):
        """
        🎯 找到最优决策阈值，重点提升特异性
        目标：特异性≥75%，敏感性≥80%
        """
        print(f"\n🎯 开始阈值优化...")
        self.model.eval()
        all_probs = []
        all_labels = []

        with torch.no_grad():
            for batch in val_loader:
                # 🔧 使用统一的数据提取方法
                ecg_data, pcg_data, labels = self.extract_data_from_sample(batch)

                # 确保数据类型正确
                ecg_data = ecg_data.float()
                pcg_data = pcg_data.float()
                labels = labels.long()

                # 确保标签维度正确
                if labels.dim() > 1:
                    labels = labels.squeeze()

                outputs = self.model(ecg_data, pcg_data)
                probs = torch.softmax(outputs, dim=1)[:, 1]  # 异常类别概率

                all_probs.extend(probs.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())

        # 计算不同阈值下的性能
        thresholds = np.arange(0.1, 0.9, 0.01)
        best_threshold = 0.5
        best_score = 0
        results = []

        for threshold in thresholds:
            predictions = (np.array(all_probs) >= threshold).astype(int)

            # 计算混淆矩阵
            from sklearn.metrics import confusion_matrix
            tn, fp, fn, tp = confusion_matrix(all_labels, predictions).ravel()

            # 计算指标
            specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
            sensitivity = tp / (tp + fn) if (tp + fn) > 0 else 0
            f1 = 2 * tp / (2 * tp + fp + fn) if (2 * tp + fp + fn) > 0 else 0
            accuracy = (tp + tn) / (tp + tn + fp + fn)

            # 🎯 新的优化目标：重点提升特异性，同时保持合理的敏感性
            # 要求特异性至少70%，敏感性至少80%
            if specificity >= 0.70 and sensitivity >= 0.80:
                # 在满足最低要求的基础上，优化平衡分数
                balanced_score = 0.4 * f1 + 0.6 * specificity  # 更重视特异性
            else:
                # 不满足最低要求的阈值给予惩罚
                balanced_score = 0.2 * f1 + 0.3 * specificity

            results.append({
                'threshold': threshold,
                'specificity': specificity,
                'sensitivity': sensitivity,
                'f1': f1,
                'accuracy': accuracy,
                'balanced_score': balanced_score,
                'tp': tp, 'tn': tn, 'fp': fp, 'fn': fn
            })

            if balanced_score > best_score:
                best_threshold = threshold
                best_score = balanced_score

        # 找到最佳结果
        best_result = max(results, key=lambda x: x['balanced_score'])

        print(f"🎯 阈值优化结果:")
        print(f"   最优阈值: {best_result['threshold']:.3f}")
        print(f"   特异性: {best_result['specificity']:.4f} ({best_result['specificity']*100:.2f}%)")
        print(f"   敏感性: {best_result['sensitivity']:.4f} ({best_result['sensitivity']*100:.2f}%)")
        print(f"   F1分数: {best_result['f1']:.4f}")
        print(f"   准确率: {best_result['accuracy']:.4f} ({best_result['accuracy']*100:.2f}%)")
        print(f"   混淆矩阵: TP={best_result['tp']}, TN={best_result['tn']}, FP={best_result['fp']}, FN={best_result['fn']}")

        # 更新最优阈值
        self.optimal_threshold = best_threshold
        return best_threshold, best_result

    def freeze_pretrained_layers(self):
        """
        🔥 冻结预训练层 - 减少参数量，提升训练稳定性
        """
        print(f"\n🔥 开始冻结预训练层...")

        # 冻结PCG编码器的前两层（ResNet34的layer1和layer2）
        if hasattr(self.model, 'pcg_encoder') and hasattr(self.model.pcg_encoder, 'backbone'):
            backbone = self.model.pcg_encoder.backbone

            # 冻结早期卷积层
            for param in backbone.conv1.parameters():
                param.requires_grad = False
            for param in backbone.bn1.parameters():
                param.requires_grad = False

            # 冻结layer1和layer2
            if hasattr(backbone, 'layer1'):
                for param in backbone.layer1.parameters():
                    param.requires_grad = False
                print(f"   ❄️ 冻结PCG编码器layer1")

            if hasattr(backbone, 'layer2'):
                for param in backbone.layer2.parameters():
                    param.requires_grad = False
                print(f"   ❄️ 冻结PCG编码器layer2")

        # 统计可训练参数
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        frozen_params = total_params - trainable_params

        print(f"🔥 预训练层冻结完成:")
        print(f"   📊 总参数: {total_params:,}")
        print(f"   🔥 可训练参数: {trainable_params:,} ({trainable_params/total_params*100:.1f}%)")
        print(f"   ❄️ 冻结参数: {frozen_params:,} ({frozen_params/total_params*100:.1f}%)")

    def setup_differential_learning_rates(self, base_lr):
        """
        🔥 设置差分学习率 - 预训练层使用较小学习率，新层使用较大学习率
        """
        print(f"\n🔥 设置差分学习率...")

        param_groups = []

        # 预训练层参数组（较小学习率）
        pretrained_params = []
        if hasattr(self.model, 'pcg_encoder') and hasattr(self.model.pcg_encoder, 'backbone'):
            backbone = self.model.pcg_encoder.backbone
            for name, param in backbone.named_parameters():
                if param.requires_grad:  # 只包含未冻结的参数
                    pretrained_params.append(param)

        if pretrained_params:
            param_groups.append({
                'params': pretrained_params,
                'lr': base_lr * 0.1,  # 预训练层使用1/10的学习率
                'name': 'pretrained'
            })
            print(f"   🔥 预训练层学习率: {base_lr * 0.1:.6f} ({len(pretrained_params)}个参数组)")

        # 新层参数组（正常学习率）
        new_layer_params = []
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                # 检查是否属于预训练层
                is_pretrained = False
                if hasattr(self.model, 'pcg_encoder') and hasattr(self.model.pcg_encoder, 'backbone'):
                    if name.startswith('pcg_encoder.backbone.'):
                        is_pretrained = True

                if not is_pretrained:
                    new_layer_params.append(param)

        if new_layer_params:
            param_groups.append({
                'params': new_layer_params,
                'lr': base_lr,  # 新层使用正常学习率
                'name': 'new_layers'
            })
            print(f"   🔥 新层学习率: {base_lr:.6f} ({len(new_layer_params)}个参数组)")

        print(f"🔥 差分学习率设置完成: {len(param_groups)}个参数组")
        return param_groups

    def check_early_stopping(self, current_macro_f1, epoch):
        """
        🔥 检查早停条件 - 基于Macro F1-Score
        """
        improved = current_macro_f1 > (self.best_macro_f1 + self.min_delta)

        if improved:
            self.best_macro_f1 = current_macro_f1
            self.patience_counter = 0
            print(f"   🎉 Macro F1-Score提升: {current_macro_f1:.4f} (新最佳)")
            return False  # 不早停
        else:
            self.patience_counter += 1
            print(f"   ⏰ 无改善 {self.patience_counter}/{self.patience} (当前: {current_macro_f1:.4f}, 最佳: {self.best_macro_f1:.4f})")

            if self.patience_counter >= self.patience:
                print(f"   🛑 触发早停! 连续{self.patience}个epoch无改善")
                self.early_stop_triggered = True
                return True  # 早停

        return False  # 不早停

    def compute_loss(self, outputs, targets):
        """
        计算损失 - 可选择使用Focal Loss或加权交叉熵
        """
        if self.use_focal_loss:
            return self.focal_loss(outputs, targets)
        else:
            return self.criterion(outputs, targets)

    def setup_realtime_plotting(self):
        """设置实时绘图"""
        plt.style.use('default')
        plt.rcParams['font.family'] = 'DejaVu Sans'
        plt.rcParams['figure.figsize'] = (15, 10)

    def update_realtime_plot(self):
        """更新实时训练曲线图"""
        if len(self.train_losses) == 0:
            return

        # 创建图形
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Real-time Training Progress', fontsize=16, fontweight='bold')

        epochs = list(range(1, len(self.train_losses) + 1))

        # 1. 损失曲线
        ax1 = axes[0, 0]
        ax1.plot(epochs, self.train_losses, 'o-', label='Train Loss', linewidth=2, markersize=4)
        if self.val_losses and len(self.val_losses) > 0:
            ax1.plot(epochs, self.val_losses, 's-', label='Validation Loss', linewidth=2, markersize=4)
        ax1.set_title('Loss Curves', fontsize=12, fontweight='bold')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. 准确率曲线
        ax2 = axes[0, 1]
        ax2.plot(epochs, [acc*100 for acc in self.train_accuracies], 'o-', label='Train Accuracy', linewidth=2, markersize=4)
        if self.val_accuracies and len(self.val_accuracies) > 0:
            ax2.plot(epochs, [acc*100 for acc in self.val_accuracies], 's-', label='Validation Accuracy', linewidth=2, markersize=4)
        ax2.set_title('Accuracy Curves', fontsize=12, fontweight='bold')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Accuracy (%)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 3. F1分数曲线
        ax3 = axes[1, 0]
        ax3.plot(epochs, self.train_f1s, 'o-', label='Train F1', linewidth=2, markersize=4)
        if self.val_f1s and len(self.val_f1s) > 0:
            ax3.plot(epochs, self.val_f1s, 's-', label='Validation F1', linewidth=2, markersize=4)
        ax3.set_title('F1 Score Curves', fontsize=12, fontweight='bold')
        ax3.set_xlabel('Epoch')
        ax3.set_ylabel('F1 Score')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 4. 精确率和召回率
        ax4 = axes[1, 1]
        ax4.plot(epochs, self.train_precisions, 'o-', label='Train Precision', linewidth=2, markersize=4)
        ax4.plot(epochs, self.train_recalls, '^-', label='Train Recall', linewidth=2, markersize=4)
        if self.val_precisions and len(self.val_precisions) > 0:
            ax4.plot(epochs, self.val_precisions, 's-', label='Val Precision', linewidth=2, markersize=4)
            ax4.plot(epochs, self.val_recalls, 'v-', label='Val Recall', linewidth=2, markersize=4)
        ax4.set_title('Precision & Recall', fontsize=12, fontweight='bold')
        ax4.set_xlabel('Epoch')
        ax4.set_ylabel('Score')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存简洁版本（中文）
        plot_path = os.path.join(self.results_dir, 'realtime_training_curves.png')
        plt.savefig(plot_path, dpi=150, bbox_inches='tight')
        plt.close()  # 关闭当前图形

        # 生成详细的英文分析图
        self.create_detailed_analysis_plot()

    def create_detailed_analysis_plot(self):
        """创建详细的训练分析图（英文版本）"""
        if len(self.train_losses) < 1:
            return

        # 设置英文字体
        plt.rcParams['font.family'] = 'DejaVu Sans'
        plt.rcParams['font.size'] = 10

        # 创建更大的图形，包含更多分析内容
        fig = plt.figure(figsize=(20, 12))

        # 主标题
        fig.suptitle('Detailed Training Analysis\nMultimodal ECG-PCG Classification',
                    fontsize=18, fontweight='bold', y=0.95)

        epochs = list(range(1, len(self.train_losses) + 1))

        # 创建网格布局 (3行4列)
        gs = fig.add_gridspec(3, 4, hspace=0.3, wspace=0.3)

        # 第一行：主要训练指标
        # 损失曲线
        ax1 = fig.add_subplot(gs[0, 0])
        ax1.plot(epochs, self.train_losses, 'b-', label='Training Loss', linewidth=2, marker='o', markersize=4)
        if self.val_losses:
            ax1.plot(epochs, self.val_losses, 'r-', label='Validation Loss', linewidth=2, marker='s', markersize=4)
        ax1.set_title('Loss Curves', fontweight='bold')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 准确率曲线
        ax2 = fig.add_subplot(gs[0, 1])
        ax2.plot(epochs, self.train_accuracies, 'b-', label='Training Accuracy', linewidth=2, marker='o', markersize=4)
        if self.val_accuracies:
            ax2.plot(epochs, self.val_accuracies, 'r-', label='Validation Accuracy', linewidth=2, marker='s', markersize=4)
        ax2.set_title('Accuracy Curves', fontweight='bold')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Accuracy')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 精确率曲线
        ax3 = fig.add_subplot(gs[0, 2])
        ax3.plot(epochs, self.train_precisions, 'b-', label='Training Precision', linewidth=2, marker='o', markersize=4)
        if self.val_precisions:
            ax3.plot(epochs, self.val_precisions, 'r-', label='Validation Precision', linewidth=2, marker='s', markersize=4)
        ax3.set_title('Precision Curves', fontweight='bold')
        ax3.set_xlabel('Epoch')
        ax3.set_ylabel('Precision')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 召回率曲线
        ax4 = fig.add_subplot(gs[0, 3])
        ax4.plot(epochs, self.train_recalls, 'b-', label='Training Recall', linewidth=2, marker='o', markersize=4)
        if self.val_recalls:
            ax4.plot(epochs, self.val_recalls, 'r-', label='Validation Recall', linewidth=2, marker='s', markersize=4)
        ax4.set_title('Recall Curves', fontweight='bold')
        ax4.set_xlabel('Epoch')
        ax4.set_ylabel('Recall')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        # 第二行：性能分析
        # 训练-验证差异分析
        ax5 = fig.add_subplot(gs[1, 0])
        if self.val_losses and len(self.val_losses) == len(self.train_losses):
            loss_diff = [abs(t - v) for t, v in zip(self.train_losses, self.val_losses)]
            ax5.plot(epochs, loss_diff, 'g-', label='Loss Difference', linewidth=2, marker='d', markersize=4)
            ax5.set_title('Train-Val Loss Difference', fontweight='bold')
            ax5.set_xlabel('Epoch')
            ax5.set_ylabel('|Train Loss - Val Loss|')
            ax5.legend()
            ax5.grid(True, alpha=0.3)
        else:
            ax5.text(0.5, 0.5, 'No Validation Data', ha='center', va='center', transform=ax5.transAxes)
            ax5.set_title('Train-Val Loss Difference', fontweight='bold')

        # F1分数对比
        ax6 = fig.add_subplot(gs[1, 1])
        ax6.plot(epochs, self.train_f1s, 'b-', label='Training F1', linewidth=2, marker='o', markersize=4)
        if self.val_f1s:
            ax6.plot(epochs, self.val_f1s, 'r-', label='Validation F1', linewidth=2, marker='s', markersize=4)
        ax6.set_title('F1-Score Curves', fontweight='bold')
        ax6.set_xlabel('Epoch')
        ax6.set_ylabel('F1-Score')
        ax6.legend()
        ax6.grid(True, alpha=0.3)

        # 性能指标柱状图（最新epoch）
        ax7 = fig.add_subplot(gs[1, 2])
        if len(epochs) > 0:
            latest_metrics = {
                'Accuracy': self.train_accuracies[-1],
                'Precision': self.train_precisions[-1],
                'Recall': self.train_recalls[-1],
                'F1-Score': self.train_f1s[-1]
            }

            val_metrics = {}
            if (self.val_accuracies and self.val_precisions and
                self.val_recalls and self.val_f1s):
                val_metrics = {
                    'Accuracy': self.val_accuracies[-1],
                    'Precision': self.val_precisions[-1],
                    'Recall': self.val_recalls[-1],
                    'F1-Score': self.val_f1s[-1]
                }

            x = range(len(latest_metrics))
            width = 0.35

            ax7.bar([i - width/2 for i in x], latest_metrics.values(), width,
                   label='Training', color='skyblue', alpha=0.8)

            if val_metrics:
                ax7.bar([i + width/2 for i in x], val_metrics.values(), width,
                       label='Validation', color='lightcoral', alpha=0.8)

            ax7.set_title(f'Final Metrics Comparison\n(Epoch {len(epochs)})', fontweight='bold')
            ax7.set_ylabel('Score')
            ax7.set_xticks(x)
            ax7.set_xticklabels(latest_metrics.keys(), rotation=45)
            ax7.legend()
            ax7.grid(True, alpha=0.3, axis='y')
            ax7.set_ylim(0, 1)

        # 学习率变化（如果有的话）
        ax8 = fig.add_subplot(gs[1, 3])
        # 这里可以添加学习率变化的可视化
        ax8.text(0.5, 0.5, 'Learning Rate Schedule\n(Future Enhancement)',
                ha='center', va='center', transform=ax8.transAxes)
        ax8.set_title('Learning Rate Schedule', fontweight='bold')

        # 第三行：统计信息和总结
        ax9 = fig.add_subplot(gs[2, :])
        ax9.axis('off')

        # 添加统计信息文本
        stats_text = self.generate_training_statistics()
        ax9.text(0.05, 0.9, stats_text, transform=ax9.transAxes, fontsize=11,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))

        # 保存详细分析图
        plot_path_en = os.path.join(self.results_dir, 'realtime_training_curves_english.png')
        plt.savefig(plot_path_en, dpi=150, bbox_inches='tight')
        plt.close()

        # 恢复默认字体设置
        plt.rcParams['font.family'] = 'DejaVu Sans'
        plt.rcParams['font.size'] = 12

    def generate_training_statistics(self):
        """生成训练统计信息文本"""
        if len(self.train_losses) == 0:
            return "No training data available"

        stats = []
        stats.append("=== TRAINING STATISTICS ===")
        stats.append(f"Total Epochs: {len(self.train_losses)}")
        stats.append(f"Dataset Size: Train={len(self.train_dataset)}")

        if hasattr(self, 'val_dataset') and self.val_dataset:
            stats.append(f"              Validation={len(self.val_dataset)}")

        stats.append(f"Batch Size: {self.batch_size}")
        stats.append(f"Learning Rate: {self.learning_rate}")
        stats.append("")

        # 最佳性能
        best_train_acc = max(self.train_accuracies) if self.train_accuracies else 0
        best_train_epoch = self.train_accuracies.index(best_train_acc) + 1 if self.train_accuracies else 0

        stats.append("=== BEST PERFORMANCE ===")
        stats.append(f"Best Training Accuracy: {best_train_acc:.4f} (Epoch {best_train_epoch})")

        if self.val_accuracies:
            best_val_acc = max(self.val_accuracies)
            best_val_epoch = self.val_accuracies.index(best_val_acc) + 1
            stats.append(f"Best Validation Accuracy: {best_val_acc:.4f} (Epoch {best_val_epoch})")

        # 最新性能
        stats.append("")
        stats.append("=== LATEST PERFORMANCE ===")
        if len(self.train_losses) > 0:
            stats.append(f"Latest Training Loss: {self.train_losses[-1]:.4f}")
            stats.append(f"Latest Training Accuracy: {self.train_accuracies[-1]:.4f}")

            if self.val_losses:
                stats.append(f"Latest Validation Loss: {self.val_losses[-1]:.4f}")
                stats.append(f"Latest Validation Accuracy: {self.val_accuracies[-1]:.4f}")

        return "\n".join(stats)

    def plot_confusion_matrix(self, cm, class_names, title, normalize=False):
        """绘制混淆矩阵"""
        if normalize:
            cm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
            fmt = '.2f'
        else:
            fmt = 'd'

        plt.figure(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt=fmt, cmap='Blues',
                   xticklabels=class_names, yticklabels=class_names,
                   cbar_kws={'label': 'Count' if not normalize else 'Proportion'})
        plt.title(title, fontsize=14, fontweight='bold')
        plt.xlabel('Predicted Label', fontsize=12)
        plt.ylabel('True Label', fontsize=12)
        plt.tight_layout()

        return plt.gcf()

    def update_confusion_matrices(self, current_accuracy=None, epoch_num=None):
        """更新并保存混淆矩阵 - 只保留最新和最佳"""
        if len(self.train_confusion_matrices) == 0:
            return

        # 获取最新的混淆矩阵
        latest_train_cm = self.train_confusion_matrices[-1]
        latest_val_cm = self.val_confusion_matrices[-1] if self.val_confusion_matrices else None

        # 检查是否是最佳epoch
        is_best_epoch = False
        if current_accuracy is not None and current_accuracy > self.best_accuracy:
            self.best_accuracy = current_accuracy
            self.best_epoch_num = epoch_num if epoch_num else len(self.train_confusion_matrices)
            self.best_epoch_cm = {
                'train_cm': latest_train_cm.copy(),
                'val_cm': latest_val_cm.copy() if latest_val_cm is not None else None,
                'epoch': self.best_epoch_num,
                'accuracy': current_accuracy
            }
            is_best_epoch = True

        # 生成最新epoch的混淆矩阵
        self._save_confusion_matrix(latest_train_cm, latest_val_cm,
                                   f"Latest Epoch {len(self.train_confusion_matrices)}",
                                   "latest_confusion_matrix.png")

        # 如果是最佳epoch，也保存最佳版本
        if is_best_epoch:
            self._save_confusion_matrix(latest_train_cm, latest_val_cm,
                                       f"Best Epoch {self.best_epoch_num} (Acc: {self.best_accuracy:.3f})",
                                       "best_confusion_matrix.png")

        # 计算并显示详细指标
        self.print_confusion_matrix_metrics(latest_train_cm, latest_val_cm)

    def _save_confusion_matrix(self, train_cm, val_cm, title_suffix, filename):
        """保存混淆矩阵图片"""
        # 创建混淆矩阵图
        if val_cm is not None:
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle(f'Confusion Matrices - {title_suffix}',
                        fontsize=16, fontweight='bold')

            # 训练集混淆矩阵 - 原始计数
            plt.subplot(2, 2, 1)
            sns.heatmap(train_cm, annot=True, fmt='d', cmap='Blues',
                       xticklabels=self.class_names, yticklabels=self.class_names)
            plt.title('Train Set - Raw Counts', fontsize=12, fontweight='bold')
            plt.xlabel('Predicted Label')
            plt.ylabel('True Label')

            # 训练集混淆矩阵 - 归一化
            plt.subplot(2, 2, 2)
            train_cm_norm = train_cm.astype('float') / train_cm.sum(axis=1)[:, np.newaxis]
            sns.heatmap(train_cm_norm, annot=True, fmt='.2f', cmap='Blues',
                       xticklabels=self.class_names, yticklabels=self.class_names)
            plt.title('Train Set - Normalized', fontsize=12, fontweight='bold')
            plt.xlabel('Predicted Label')
            plt.ylabel('True Label')

            # 验证集混淆矩阵 - 原始计数
            plt.subplot(2, 2, 3)
            sns.heatmap(val_cm, annot=True, fmt='d', cmap='Oranges',
                       xticklabels=self.class_names, yticklabels=self.class_names)
            plt.title('Validation Set - Raw Counts', fontsize=12, fontweight='bold')
            plt.xlabel('Predicted Label')
            plt.ylabel('True Label')

            # 验证集混淆矩阵 - 归一化
            plt.subplot(2, 2, 4)
            val_cm_norm = val_cm.astype('float') / val_cm.sum(axis=1)[:, np.newaxis]
            sns.heatmap(val_cm_norm, annot=True, fmt='.2f', cmap='Oranges',
                       xticklabels=self.class_names, yticklabels=self.class_names)
            plt.title('Validation Set - Normalized', fontsize=12, fontweight='bold')
            plt.xlabel('Predicted Label')
            plt.ylabel('True Label')

        else:
            # 只有训练集的情况
            fig, axes = plt.subplots(1, 2, figsize=(12, 5))
            fig.suptitle(f'Training Confusion Matrix - {title_suffix}',
                        fontsize=16, fontweight='bold')

            # 训练集混淆矩阵 - 原始计数
            plt.subplot(1, 2, 1)
            sns.heatmap(train_cm, annot=True, fmt='d', cmap='Blues',
                       xticklabels=self.class_names, yticklabels=self.class_names)
            plt.title('Raw Counts', fontsize=12, fontweight='bold')
            plt.xlabel('Predicted Label')
            plt.ylabel('True Label')

            # 训练集混淆矩阵 - 归一化
            plt.subplot(1, 2, 2)
            train_cm_norm = train_cm.astype('float') / train_cm.sum(axis=1)[:, np.newaxis]
            sns.heatmap(train_cm_norm, annot=True, fmt='.2f', cmap='Blues',
                       xticklabels=self.class_names, yticklabels=self.class_names)
            plt.title('Normalized', fontsize=12, fontweight='bold')
            plt.xlabel('Predicted Label')
            plt.ylabel('True Label')

        plt.tight_layout()

        # 保存混淆矩阵图
        cm_path = os.path.join(self.results_dir, filename)
        plt.savefig(cm_path, dpi=150, bbox_inches='tight')
        plt.close()

    def print_confusion_matrix_metrics(self, train_cm, val_cm=None):
        """从混淆矩阵计算并打印详细指标"""

        def calculate_metrics_from_cm(cm, dataset_name):
            """从混淆矩阵计算指标"""
            # 对于二分类
            if cm.shape == (2, 2):
                tn, fp, fn, tp = cm.ravel()

                # 计算指标
                accuracy = (tp + tn) / (tp + tn + fp + fn)
                precision = tp / (tp + fp) if (tp + fp) > 0 else 0
                recall = tp / (tp + fn) if (tp + fn) > 0 else 0
                specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
                f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0

                self.log_and_print(f"   📊 {dataset_name} Confusion Matrix Metrics:")
                self.log_and_print(f"      True Positives (TP): {tp}")
                self.log_and_print(f"      True Negatives (TN): {tn}")
                self.log_and_print(f"      False Positives (FP): {fp}")
                self.log_and_print(f"      False Negatives (FN): {fn}")
                self.log_and_print(f"      Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
                self.log_and_print(f"      Precision: {precision:.4f} ({precision*100:.2f}%)")
                self.log_and_print(f"      Recall (Sensitivity): {recall:.4f} ({recall*100:.2f}%)")
                self.log_and_print(f"      Specificity: {specificity:.4f} ({specificity*100:.2f}%)")
                self.log_and_print(f"      F1-Score: {f1:.4f}")

                return {
                    'accuracy': accuracy, 'precision': precision, 'recall': recall,
                    'specificity': specificity, 'f1': f1, 'tp': tp, 'tn': tn, 'fp': fp, 'fn': fn
                }
            else:
                # 多分类情况
                total = np.sum(cm)
                accuracy = np.trace(cm) / total
                self.log_and_print(f"   📊 {dataset_name} Confusion Matrix Metrics:")
                self.log_and_print(f"      Overall Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
                return {'accuracy': accuracy}

        self.log_and_print(f"\n🔍 Confusion Matrix Analysis:")

        # 训练集指标
        train_metrics = calculate_metrics_from_cm(train_cm, "Training")

        # 验证集指标
        if val_cm is not None:
            val_metrics = calculate_metrics_from_cm(val_cm, "Validation")

            # 比较训练集和验证集
            if 'precision' in train_metrics and 'precision' in val_metrics:
                self.log_and_print(f"\n📈 Train vs Validation Comparison:")
                self.log_and_print(f"   Accuracy Gap: {abs(train_metrics['accuracy'] - val_metrics['accuracy'])*100:.2f}%")
                self.log_and_print(f"   Precision Gap: {abs(train_metrics['precision'] - val_metrics['precision'])*100:.2f}%")
                self.log_and_print(f"   Recall Gap: {abs(train_metrics['recall'] - val_metrics['recall'])*100:.2f}%")

    def log_and_print(self, message):
        """同时打印到控制台和写入日志文件"""
        print(message)
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(message + '\n')
            f.flush()  # 立即写入文件
        
    def extract_data_from_sample(self, sample):
        """从数据集样本中提取ECG、PCG和标签，并移动到正确的设备"""
        try:
            if isinstance(sample, dict):
                # 如果是字典格式（已经是批处理后的数据）
                ecg = sample['ecg']
                pcg = sample['pcg']
                label = sample['label']

                # 🔧 真正的多模态数据处理：ECG 1D信号 + PCG 2D频谱图
                # ECG: 从 [batch, 16000] 转换为 [batch, 1, 16000] (1D信号)
                if isinstance(ecg, np.ndarray):
                    ecg = torch.from_numpy(ecg).float()
                elif isinstance(ecg, torch.Tensor):
                    ecg = ecg.float()  # 确保是float32类型
                if ecg.dim() == 2:  # [batch, 16000] - 1D信号
                    ecg = ecg.unsqueeze(1)  # 添加通道维度: [batch, 16000] -> [batch, 1, 16000]

                # 🔧 ECG数据标准化 - 解决不同样本幅度差异巨大的问题
                # 按样本标准化，因为正常和异常样本的幅度差异达到3-5倍
                ecg_mean = ecg.mean(dim=2, keepdim=True)  # [batch, 1, 1]
                ecg_std = ecg.std(dim=2, keepdim=True) + 1e-8  # [batch, 1, 1]
                ecg = (ecg - ecg_mean) / ecg_std

                # PCG: 从 [batch, 128, 126] 转换为 [batch, 1, 128, 126] (添加通道维度)
                if isinstance(pcg, np.ndarray):
                    pcg = torch.from_numpy(pcg).float()
                elif isinstance(pcg, torch.Tensor):
                    pcg = pcg.float()  # 确保是float32类型
                if pcg.dim() == 3:  # [batch, 128, 126] - 频谱图
                    pcg = pcg.unsqueeze(1)  # 添加通道维度: [batch, 128, 126] -> [batch, 1, 128, 126]

                # 🔧 PCG数据归一化 - 确保ECG和PCG在相同的数值范围
                pcg_mean = pcg.mean(dim=(2, 3), keepdim=True)
                pcg_std = pcg.std(dim=(2, 3), keepdim=True) + 1e-8
                pcg = (pcg - pcg_mean) / pcg_std
            elif isinstance(sample, (tuple, list)) and len(sample) >= 2:
                # 如果是tuple格式，需要从数据集获取完整信息
                # 这里我们需要重新获取数据
                data, label = sample
                # 暂时使用ECG数据，PCG用零填充（这不是最优解，但可以先运行）
                ecg = data
                # 🔧 确保PCG数据在正确设备上创建
                if hasattr(data, 'device'):
                    pcg = torch.zeros(data.shape[0], 1, 128, 128, device=data.device, dtype=data.dtype)
                else:
                    pcg = torch.zeros(data.shape[0], 1, 128, 128, dtype=torch.float32)
            else:
                raise ValueError(f"不支持的数据格式: {type(sample)}")

            # 🔧 安全地移动到目标设备
            if not isinstance(ecg, torch.Tensor):
                ecg = torch.tensor(ecg, dtype=torch.float32)
            if not isinstance(pcg, torch.Tensor):
                pcg = torch.tensor(pcg, dtype=torch.float32)
            if not isinstance(label, torch.Tensor):
                label = torch.tensor(label, dtype=torch.long)

            # 🔧 确保标签是1D张量（CrossEntropyLoss要求）
            if label.dim() > 1:
                label = label.squeeze()  # 移除多余的维度
            if label.dim() == 0:
                label = label.unsqueeze(0)  # 如果是标量，添加batch维度

            # 移动到目标设备
            ecg = ecg.to(self.device, non_blocking=True)
            pcg = pcg.to(self.device, non_blocking=True)
            label = label.to(self.device, non_blocking=True)

            # 🔧 条件性应用数据增强
            if self.model.training and self.enable_augmentation:  # 只在训练时且启用增强时应用
                ecg, pcg = self.augmenter(ecg, pcg)

            return ecg, pcg, label

        except Exception as e:
            print(f"❌ 数据提取错误: {e}")
            print(f"   样本类型: {type(sample)}")
            if isinstance(sample, (tuple, list)):
                print(f"   样本长度: {len(sample)}")
                for i, item in enumerate(sample):
                    print(f"   元素{i}: {type(item)}, 形状: {getattr(item, 'shape', 'N/A')}")
            raise
    
    def train_epoch(self, epoch_num=None):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        all_predictions = []
        all_labels = []

        # 创建训练进度条 - 包含epoch信息
        desc = f"Train Epoch {epoch_num}" if epoch_num is not None else "Train"
        train_progress = tqdm(self.train_dataloader, desc=desc)

        for batch_idx, sample in enumerate(train_progress):
            try:
                # 提取数据
                ecg, pcg, labels = self.extract_data_from_sample(sample)

                # 🔧 调试信息：检查数据形状
                if batch_idx == 0:
                    print(f"🔍 批次 {batch_idx} 数据形状调试:")
                    print(f"   原始样本类型: {type(sample)}")
                    if isinstance(sample, dict):
                        print(f"   样本ECG形状: {sample['ecg'].shape if hasattr(sample['ecg'], 'shape') else 'N/A'}")
                        print(f"   样本PCG形状: {sample['pcg'].shape if hasattr(sample['pcg'], 'shape') else 'N/A'}")
                    print(f"   提取后ECG形状: {ecg.shape}")
                    print(f"   提取后PCG形状: {pcg.shape}")
                    print(f"   提取后标签形状: {labels.shape}")

                # 移到设备（数据已经在正确设备上了，但确保类型正确）
                ecg = ecg.float()
                pcg = pcg.float()
                labels = labels.long()

                # 🔥 应用数据增强
                if self.enable_augmentation and self.augmenter is not None:
                    ecg, pcg = self.augmenter(ecg, pcg)

                # 🔥 应用幅度缩放增强
                amplitude_scaled = False
                if self.enable_augmentation and hasattr(self, 'amplitude_scaling_p'):
                    ecg, pcg, amplitude_scaled = self.apply_amplitude_scaling(ecg, pcg)

                # 确保标签是正确的形状
                if labels.dim() > 1:
                    labels = labels.squeeze()

                # 🔥 应用Mixup增强 (60%概率)
                use_mixup = (self.enable_augmentation and
                           random.random() < self.mixup_prob and
                           self.mixup_alpha > 0)

                # 前向传播
                self.optimizer.zero_grad()

                if use_mixup:
                    # 使用Mixup
                    mixed_ecg, mixed_pcg, y_a, y_b, lam = self.mixup_data(ecg, pcg, labels, self.mixup_alpha)
                    outputs = self.model(mixed_ecg, mixed_pcg)
                    loss = self.mixup_criterion(outputs, y_a, y_b, lam)
                    # Mixup时使用原始标签计算预测准确率
                    predictions = torch.argmax(outputs, dim=1)
                else:
                    # 正常训练
                    outputs = self.model(ecg, pcg)
                    loss = self.compute_loss(outputs, labels)
                    predictions = torch.argmax(outputs, dim=1)

                # 反向传播
                loss.backward()
                self.optimizer.step()
                # 注意：学习率调度现在在epoch级别处理
                
                # 统计
                total_loss += loss.item()
                all_predictions.extend(predictions.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())

                # 更新进度条信息（减少更新频率）
                if batch_idx % 20 == 0:
                    current_lr = self.optimizer.param_groups[0]['lr']
                    # 🔥 增强状态显示
                    aug_status = []
                    if use_mixup:
                        aug_status.append("🎯Mix")
                    if amplitude_scaled:
                        aug_status.append("📈Amp")
                    aug_display = "+".join(aug_status) if aug_status else "📊"

                    train_progress.set_postfix(
                        Loss=f'{loss.item():.3f}',
                        LR=f'{current_lr:.1e}',
                        Aug=aug_display
                    )
                
            except Exception as e:
                print(f"批次 {batch_idx} 训练出错: {e}")
                continue
        
        # 计算平均损失和指标
        avg_loss = total_loss / len(self.train_dataloader)
        accuracy = accuracy_score(all_labels, all_predictions)
        precision, recall, f1, _ = precision_recall_fscore_support(all_labels, all_predictions, average='weighted', zero_division=0)

        # 计算混淆矩阵
        train_cm = confusion_matrix(all_labels, all_predictions)
        self.train_confusion_matrices.append(train_cm)

        return avg_loss, accuracy, precision, recall, f1

    def validate_epoch(self, epoch_num=None):
        """验证一个epoch"""
        print(f"🔍 开始验证epoch {epoch_num}...")

        if self.val_dataloader is None:
            print("❌ 验证集数据加载器为空!")
            return None, None, None, None, None

        print(f"验证集批次数量: {len(self.val_dataloader)}")

        self.model.eval()
        total_loss = 0
        all_predictions = []
        all_labels = []
        all_probabilities = []  # 🔧 添加概率收集，用于ROC曲线

        # 创建验证进度条 - 包含epoch信息
        desc = f"Valid Epoch {epoch_num}" if epoch_num is not None else "Valid"
        val_progress = tqdm(self.val_dataloader, desc=desc)

        with torch.no_grad():
            for batch_idx, sample in enumerate(val_progress):
                # 提取数据
                ecg, pcg, labels = self.extract_data_from_sample(sample)

                # 验证数据设备（只在第一个batch打印）
                if batch_idx == 0:
                    print(f"验证数据设备检查:")
                    print(f"  ECG设备: {ecg.device}, 形状: {ecg.shape}")
                    print(f"  PCG设备: {pcg.device}, 形状: {pcg.shape}")
                    print(f"  标签设备: {labels.device}, 形状: {labels.shape}")

                # 前向传播
                outputs = self.model(ecg, pcg)
                loss = self.compute_loss(outputs, labels)  # 使用新的损失计算函数

                # 统计
                total_loss += loss.item()
                # 🔧 获取概率用于ROC曲线和阈值优化
                probabilities = torch.softmax(outputs, dim=1)[:, 1]  # 获取正类概率

                # 🎯 使用最优阈值进行预测（如果已优化）
                if hasattr(self, 'optimal_threshold') and self.optimal_threshold != 0.5:
                    predictions = (probabilities >= self.optimal_threshold).long()
                else:
                    predictions = torch.argmax(outputs, dim=1)

                all_predictions.extend(predictions.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
                all_probabilities.extend(probabilities.cpu().numpy())  # 🔧 收集概率

                # 更新验证进度条信息
                if batch_idx % 20 == 0:
                    val_progress.set_postfix(Loss=f'{loss.item():.3f}')

        # 计算平均损失和指标
        if len(all_predictions) == 0 or len(all_labels) == 0:
            print("⚠️ 验证过程中没有收集到有效数据！")
            return 0.0, 0.0, 0.0, 0.0, 0.0

        avg_loss = total_loss / len(self.val_dataloader) if len(self.val_dataloader) > 0 else 0.0
        accuracy = accuracy_score(all_labels, all_predictions)
        precision, recall, f1, _ = precision_recall_fscore_support(all_labels, all_predictions, average='weighted', zero_division=0)

        # 计算混淆矩阵
        val_cm = confusion_matrix(all_labels, all_predictions)
        self.val_confusion_matrices.append(val_cm)

        # 🔧 计算 ROC 曲线和 AUC
        try:
            # 计算 AUC
            auc_score = roc_auc_score(all_labels, all_probabilities)

            # 计算 ROC 曲线
            fpr, tpr, thresholds = roc_curve(all_labels, all_probabilities)

            # 保存 ROC 数据用于绘图
            if not hasattr(self, 'roc_data'):
                self.roc_data = []
            self.roc_data.append({
                'epoch': epoch_num,
                'fpr': fpr,
                'tpr': tpr,
                'auc': auc_score,
                'thresholds': thresholds
            })

            print(f"📊 AUC Score: {auc_score:.4f}")

        except Exception as e:
            print(f"⚠️ ROC/AUC 计算失败: {e}")
            auc_score = 0.0

        # 🔥 保存验证标签和预测用于早停机制
        self.val_all_labels = all_labels
        self.val_all_predictions = all_predictions

        print(f"验证完成: 处理了 {len(all_predictions)} 个样本")
        return avg_loss, accuracy, precision, recall, f1, auc_score

    def clean_old_plots(self):
        """清理旧的图片文件"""
        plot_files = [
            'realtime_training_curves.png',
            'realtime_training_curves_english.png',
            'latest_confusion_matrix.png',
            'best_confusion_matrix.png',
            'realtime_confusion_matrix.png',  # 旧的实时混淆矩阵
            'confusion_matrix_evolution.png'  # 旧的演化图
        ]

        for filename in plot_files:
            filepath = os.path.join(self.results_dir, filename)
            if os.path.exists(filepath):
                try:
                    os.remove(filepath)
                    self.log_and_print(f"   🗑️ 已删除旧图片: {filename}")
                except Exception as e:
                    self.log_and_print(f"   ⚠️ 删除图片失败 {filename}: {e}")

    def train(self):
        """完整训练过程"""
        self.log_and_print(f"\n📋 训练配置:")
        self.log_and_print(f"   总epoch数: {self.num_epochs}")
        self.log_and_print(f"   训练集大小: {len(self.train_dataset)}")
        if self.val_dataset is not None:
            self.log_and_print(f"   验证集大小: {len(self.val_dataset)}")
        self.log_and_print(f"   批次大小: {self.batch_size}")
        self.log_and_print(f"   学习率: {self.learning_rate}")
        self.log_and_print(f"   设备: {self.device}")
        self.log_and_print(f"   模型参数数量: {sum(p.numel() for p in self.model.parameters()):,}")
        self.log_and_print("")

        # 清理旧的图片文件
        self.clean_old_plots()
        
        best_accuracy = 0
        best_model_state = None
        
        for epoch in range(self.num_epochs):
            self.log_and_print(f"\n{'='*60}")
            self.log_and_print(f"🚀 EPOCH {epoch+1}/{self.num_epochs} 开始训练")
            self.log_and_print(f"   开始时间: {datetime.datetime.now().strftime('%H:%M:%S')}")
            self.log_and_print(f"{'='*60}")

            # 记录epoch开始时间
            epoch_start_time = datetime.datetime.now()

            # 训练
            train_loss, train_acc, train_precision, train_recall, train_f1 = self.train_epoch(epoch_num=epoch+1)

            # 验证
            if self.val_dataloader is not None:
                val_loss, val_acc, val_precision, val_recall, val_f1, val_auc = self.validate_epoch(epoch_num=epoch+1)

                # 🔥 计算Macro F1-Score用于早停和学习率调整
                if hasattr(self, 'val_all_labels') and hasattr(self, 'val_all_predictions'):
                    current_macro_f1 = self.compute_macro_f1(self.val_all_labels, self.val_all_predictions)
                else:
                    current_macro_f1 = val_f1  # 使用weighted F1作为备选

            else:
                val_loss, val_acc, val_precision, val_recall, val_f1, val_auc = None, None, None, None, None, None
                current_macro_f1 = None

            # 🔥 更新学习率 (Warmup + ReduceLROnPlateau) - 在验证后调用
            self.update_learning_rate(epoch, current_macro_f1)
            current_lr = self.optimizer.param_groups[0]['lr']

            # 计算epoch耗时
            epoch_duration = datetime.datetime.now() - epoch_start_time

            # 记录训练指标
            self.train_losses.append(train_loss)
            self.train_accuracies.append(train_acc)
            self.train_precisions.append(train_precision)
            self.train_recalls.append(train_recall)
            self.train_f1s.append(train_f1)

            # 记录验证指标
            if val_loss is not None:
                self.val_losses.append(val_loss)
                self.val_accuracies.append(val_acc)
                self.val_precisions.append(val_precision)
                self.val_recalls.append(val_recall)
                self.val_f1s.append(val_f1)

            # 🔧 条件性记录到TensorBoard
            if self.enable_tensorboard and self.writer is not None:
                self.writer.add_scalar('Loss/Train', train_loss, epoch)
                self.writer.add_scalar('Accuracy/Train', train_acc, epoch)
                self.writer.add_scalar('Precision/Train', train_precision, epoch)
                self.writer.add_scalar('Recall/Train', train_recall, epoch)
                self.writer.add_scalar('F1/Train', train_f1, epoch)

                if val_loss is not None:
                    self.writer.add_scalar('Loss/Validation', val_loss, epoch)
                    self.writer.add_scalar('Accuracy/Validation', val_acc, epoch)
                    self.writer.add_scalar('Precision/Validation', val_precision, epoch)
                    self.writer.add_scalar('Recall/Validation', val_recall, epoch)
                    self.writer.add_scalar('F1/Validation', val_f1, epoch)

                # 记录学习率
                current_lr = self.optimizer.param_groups[0]['lr']
                self.writer.add_scalar('Learning_Rate', current_lr, epoch)

            # 🔧 获取学习率（无论是否启用TensorBoard）
            current_lr = self.optimizer.param_groups[0]['lr']

            # 打印结果
            self.log_and_print(f"\n📊 EPOCH {epoch+1} 结果:")
            self.log_and_print(f"   🚂 训练集:")
            self.log_and_print(f"      损失: {train_loss:.4f}")
            self.log_and_print(f"      准确率: {train_acc:.4f} ({train_acc*100:.2f}%)")
            self.log_and_print(f"      精确率: {train_precision:.4f}")
            self.log_and_print(f"      召回率: {train_recall:.4f}")
            self.log_and_print(f"      F1分数: {train_f1:.4f}")

            if val_loss is not None:
                self.log_and_print(f"   🔍 验证集:")
                self.log_and_print(f"      损失: {val_loss:.4f}")
                self.log_and_print(f"      准确率: {val_acc:.4f} ({val_acc*100:.2f}%)")
                self.log_and_print(f"      精确率: {val_precision:.4f}")
                self.log_and_print(f"      召回率: {val_recall:.4f}")
                self.log_and_print(f"      F1分数: {val_f1:.4f}")

            self.log_and_print(f"   ⚙️ 其他:")
            self.log_and_print(f"      学习率: {current_lr:.6f}")
            self.log_and_print(f"      耗时: {epoch_duration.total_seconds():.1f}秒")

            # 🔥 早停检查 - 基于Macro F1-Score
            if current_macro_f1 is not None:
                should_stop = self.check_early_stopping(current_macro_f1, epoch+1)
                if should_stop:
                    self.log_and_print(f"\n🛑 早停触发! 在第{epoch+1}个epoch停止训练")
                    self.log_and_print(f"   📊 最佳Macro F1-Score: {self.best_macro_f1:.4f}")
                    self.log_and_print(f"   ⏰ 连续{self.patience}个epoch无改善")
                    break

            # 保存最佳模型（基于验证集准确率，如果没有验证集则基于训练集）
            current_metric = val_acc if val_acc is not None else train_acc
            metric_name = "验证准确率" if val_acc is not None else "训练准确率"

            if current_metric > best_accuracy:
                best_accuracy = current_metric
                best_model_state = self.model.state_dict().copy()
                self.log_and_print(f"   🎉 新的最佳{metric_name}: {best_accuracy:.4f} ({best_accuracy*100:.2f}%)")

                # 🎯 在最佳模型时进行阈值优化
                if self.threshold_search_enabled and self.val_dataloader is not None:
                    optimal_threshold, threshold_result = self.find_optimal_threshold(self.val_dataloader)
                    self.log_and_print(f"   🎯 最优阈值更新: {optimal_threshold:.3f}")
                    self.log_and_print(f"      特异性: {threshold_result['specificity']*100:.2f}%")
                    self.log_and_print(f"      敏感性: {threshold_result['sensitivity']*100:.2f}%")

                # 🔧 条件性记录最佳准确率到TensorBoard
                if self.enable_tensorboard and self.writer is not None:
                    self.writer.add_scalar('Accuracy/Best', best_accuracy, epoch)

            # 保存训练历史到文件
            self.save_training_history()

            # 更新实时训练曲线图
            self.update_realtime_plot()

            # 更新混淆矩阵
            self.update_confusion_matrices(current_accuracy=current_metric, epoch_num=epoch)

            # 🔧 绘制 ROC 曲线（每个 epoch 后）
            if hasattr(self, 'roc_data') and len(self.roc_data) > 0:
                try:
                    self.plot_roc_curve()
                    # 🔧 条件性记录 AUC 到 TensorBoard
                    if self.enable_tensorboard and self.writer is not None:
                        latest_auc = self.roc_data[-1]['auc']
                        self.writer.add_scalar('AUC/Validation', latest_auc, epoch)
                except Exception as e:
                    self.log_and_print(f"⚠️ ROC 曲线绘制失败: {e}")

            # 🔧 条件性刷新TensorBoard
            if self.enable_tensorboard and self.writer is not None:
                self.writer.flush()
        
        self.log_and_print(f"\n🎉 训练完成！")
        self.log_and_print(f"   最佳准确率: {best_accuracy:.4f} ({best_accuracy*100:.2f}%)")
        self.log_and_print(f"   结束时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        if self.enable_tensorboard:
            self.log_and_print(f"   TensorBoard日志: {self.tensorboard_dir}")
        self.log_and_print("="*80)

        # 🔧 条件性关闭TensorBoard writer
        if self.enable_tensorboard and self.writer is not None:
            self.writer.close()

        # 加载最佳模型
        if best_model_state is not None:
            self.model.load_state_dict(best_model_state)

        return best_model_state

    def save_training_history(self):
        """保存训练历史到CSV文件"""
        import pandas as pd

        # 创建训练历史数据
        history_data = {
            'epoch': list(range(1, len(self.train_losses) + 1)),
            'train_loss': self.train_losses,
            'train_accuracy': self.train_accuracies,
            'train_precision': self.train_precisions,
            'train_recall': self.train_recalls,
            'train_f1': self.train_f1s
        }

        # 如果有验证数据，添加验证指标
        if len(self.val_losses) > 0:
            history_data.update({
                'val_loss': self.val_losses,
                'val_accuracy': self.val_accuracies,
                'val_precision': self.val_precisions,
                'val_recall': self.val_recalls,
                'val_f1': self.val_f1s
            })

        # 保存为CSV
        df = pd.DataFrame(history_data)
        history_file = os.path.join(self.results_dir, "training_history_multimodal.csv")
        df.to_csv(history_file, index=False)

        # 也保存为文本格式
        metrics_file = os.path.join(self.results_dir, "training_metrics_multimodal.txt")
        with open(metrics_file, 'w', encoding='utf-8') as f:
            if len(self.val_losses) > 0:
                f.write("Epoch,Train_Loss,Train_Acc,Train_Prec,Train_Rec,Train_F1,Val_Loss,Val_Acc,Val_Prec,Val_Rec,Val_F1\n")
                for i in range(len(self.train_losses)):
                    f.write(f"{i+1},{self.train_losses[i]:.4f},{self.train_accuracies[i]:.4f},"
                           f"{self.train_precisions[i]:.4f},{self.train_recalls[i]:.4f},{self.train_f1s[i]:.4f},"
                           f"{self.val_losses[i]:.4f},{self.val_accuracies[i]:.4f},"
                           f"{self.val_precisions[i]:.4f},{self.val_recalls[i]:.4f},{self.val_f1s[i]:.4f}\n")
            else:
                f.write("Epoch,Train_Loss,Train_Acc,Train_Prec,Train_Rec,Train_F1\n")
                for i in range(len(self.train_losses)):
                    f.write(f"{i+1},{self.train_losses[i]:.4f},{self.train_accuracies[i]:.4f},"
                           f"{self.train_precisions[i]:.4f},{self.train_recalls[i]:.4f},{self.train_f1s[i]:.4f}\n")

    def plot_roc_curve(self, save_path=None):
        """绘制 ROC 曲线"""
        if not hasattr(self, 'roc_data') or len(self.roc_data) == 0:
            print("⚠️ 没有 ROC 数据可绘制")
            return

        plt.figure(figsize=(12, 8))

        # 绘制最新的 ROC 曲线
        latest_roc = self.roc_data[-1]
        plt.subplot(1, 2, 1)
        plt.plot(latest_roc['fpr'], latest_roc['tpr'],
                color='darkorange', lw=2,
                label=f'ROC curve (AUC = {latest_roc["auc"]:.4f})')
        plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--', label='Random')
        plt.xlim([0.0, 1.0])
        plt.ylim([0.0, 1.05])
        plt.xlabel('False Positive Rate (1 - Specificity)')
        plt.ylabel('True Positive Rate (Sensitivity)')
        plt.title(f'ROC Curve - Epoch {latest_roc["epoch"]}')
        plt.legend(loc="lower right")
        plt.grid(True, alpha=0.3)

        # 绘制 AUC 演化曲线
        plt.subplot(1, 2, 2)
        epochs = [data['epoch'] for data in self.roc_data]
        aucs = [data['auc'] for data in self.roc_data]
        plt.plot(epochs, aucs, 'b-o', linewidth=2, markersize=6)
        plt.xlabel('Epoch')
        plt.ylabel('AUC Score')
        plt.title('AUC Score Evolution')
        plt.grid(True, alpha=0.3)
        plt.ylim([0.0, 1.0])

        # 添加最佳 AUC 标记
        best_auc_idx = np.argmax(aucs)
        best_auc = aucs[best_auc_idx]
        best_epoch = epochs[best_auc_idx]
        plt.axhline(y=best_auc, color='r', linestyle='--', alpha=0.7,
                   label=f'Best AUC: {best_auc:.4f} (Epoch {best_epoch})')
        plt.legend()

        plt.tight_layout()

        # 保存图片
        if save_path is None:
            save_path = os.path.join(self.results_dir, 'roc_curves.png')

        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"📊 ROC 曲线已保存到: {save_path}")

        # 同时保存最新的 ROC 曲线（实时更新）
        realtime_path = os.path.join(self.results_dir, 'realtime_roc_curve.png')
        plt.figure(figsize=(8, 6))
        plt.plot(latest_roc['fpr'], latest_roc['tpr'],
                color='darkorange', lw=2,
                label=f'ROC curve (AUC = {latest_roc["auc"]:.4f})')
        plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--', label='Random')
        plt.xlim([0.0, 1.0])
        plt.ylim([0.0, 1.05])
        plt.xlabel('False Positive Rate (1 - Specificity)')
        plt.ylabel('True Positive Rate (Sensitivity)')
        plt.title(f'ROC Curve - Epoch {latest_roc["epoch"]} (AUC = {latest_roc["auc"]:.4f})')
        plt.legend(loc="lower right")
        plt.grid(True, alpha=0.3)
        plt.savefig(realtime_path, dpi=300, bbox_inches='tight')
        plt.close()

    def save_model(self, filepath):
        """保存模型"""
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'train_losses': self.train_losses,
            'train_accuracies': self.train_accuracies,
        }, filepath)
        self.log_and_print(f"模型已保存到: {filepath}")

name: ecgpcg-381
channels:
  - pytorch
  - anaconda
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1=conda_forge
  - _openmp_mutex=4.5=2_kmp_llvm
  - abseil-cpp=20211102.0=hd4dd3e8_0
  - absl-py=1.4.0=py38h06a4308_0
  - aiohttp=3.6.3=py38h7b6447c_0
  - async-timeout=3.0.1=py38h06a4308_0
  - attrs=22.1.0=py38h06a4308_0
  - audioread=3.0.0=py38h578d9bd_1
  - blas=1.0=mkl
  - blinker=1.4=py38h06a4308_0
  - bottleneck=1.3.5=py38h7deecbd_0
  - brotli=1.0.9=h5eee18b_7
  - brotli-bin=1.0.9=h5eee18b_7
  - brotli-python=1.0.9=py38hfa26641_7
  - bzip2=1.0.8=h7f98852_4
  - c-ares=1.19.0=h5eee18b_0
  - ca-certificates=2023.05.30=h06a4308_0
  - cachetools=4.2.2=pyhd3eb1b0_0
  - certifi=2023.7.22=py38h06a4308_0
  - cffi=1.15.0=py38h7f8727e_0
  - charset-normalizer=3.2.0=pyhd8ed1ab_0
  - click=8.0.4=py38h06a4308_0
  - cloudpickle=2.2.1=py38h06a4308_0
  - colorama=0.4.6=pyhd8ed1ab_0
  - configargparse=1.7=pyhd8ed1ab_0
  - cryptography=41.0.2=py38h774aba0_0
  - cudatoolkit=11.6.0=hecad31d_10
  - cycler=0.11.0=pyhd3eb1b0_0
  - cytoolz=0.12.0=py38h5eee18b_0
  - dask-core=2022.2.1=pyhd3eb1b0_0
  - dbus=1.13.18=hb2f20db_0
  - decorator=5.1.1=pyhd8ed1ab_0
  - distro=1.8.0=pyhd8ed1ab_0
  - expat=2.4.9=h6a678d5_0
  - ffmpeg=4.3=hf484d3e_0
  - fontconfig=2.14.1=h52c9d5c_1
  - freetype=2.10.4=h0708190_1
  - fsspec=2023.4.0=py38h06a4308_0
  - gettext=0.21.1=h27087fc_0
  - giflib=5.2.1=h5eee18b_3
  - glib=2.63.1=h5a9c865_0
  - gmp=6.2.1=h58526e2_0
  - gnutls=3.6.13=h85f3911_1
  - google-auth=2.6.0=pyhd3eb1b0_0
  - google-auth-oauthlib=0.5.2=py38h06a4308_0
  - grpc-cpp=1.48.2=h5bf31a4_0
  - grpcio=1.48.2=py38h5bf31a4_0
  - gst-plugins-base=1.14.0=hbbd80ab_1
  - gstreamer=1.14.0=hb453b48_1
  - icu=58.2=he6710b0_3
  - idna=3.4=pyhd8ed1ab_0
  - imageio=2.31.1=py38h06a4308_0
  - imageio-ffmpeg=0.4.8=pyhd8ed1ab_0
  - importlib-metadata=6.0.0=py38h06a4308_0
  - importlib_resources=5.2.0=pyhd3eb1b0_1
  - intel-openmp=2021.4.0=h06a4308_3561
  - joblib=1.3.2=pyhd8ed1ab_0
  - jpeg=9e=h166bdaf_1
  - kiwisolver=1.4.4=py38h6a678d5_0
  - lame=3.100=h7f98852_1001
  - lazy_loader=0.2=pyhd8ed1ab_0
  - lcms2=2.12=hddcbb42_0
  - ld_impl_linux-64=2.38=h1181459_1
  - libbrotlicommon=1.0.9=h5eee18b_7
  - libbrotlidec=1.0.9=h5eee18b_7
  - libbrotlienc=1.0.9=h5eee18b_7
  - libedit=3.1.20221030=h5eee18b_0
  - libffi=3.2.1=hf484d3e_1007
  - libflac=1.4.3=h59595ed_0
  - libgcc-ng=13.1.0=he5830b7_0
  - libgfortran-ng=13.1.0=h69a702a_0
  - libgfortran5=13.1.0=h15d22d2_0
  - libiconv=1.17=h166bdaf_0
  - libllvm14=14.0.6=hcd5def8_4
  - libogg=1.3.4=h7f98852_1
  - libopus=1.3.1=h7f98852_1
  - libpng=1.6.37=h21135ba_2
  - libprotobuf=3.20.3=he621ea3_0
  - librosa=0.10.0=pyhd8ed1ab_1
  - libsndfile=1.2.0=hb75c966_0
  - libstdcxx-ng=13.1.0=hfd8a6a1_0
  - libtiff=4.2.0=hecacb30_2
  - libuuid=1.41.5=h5eee18b_0
  - libvorbis=1.3.7=h9c3ff4c_0
  - libwebp=1.2.4=h11a3e52_1
  - libwebp-base=1.2.4=h5eee18b_1
  - libxcb=1.15=h7f8727e_0
  - libxml2=2.9.14=h74e7548_0
  - libzlib=1.2.13=hd590300_5
  - llvm-openmp=14.0.6=h9e868ea_0
  - llvmlite=0.40.1=py38h94a1851_0
  - locket=1.0.0=py38h06a4308_0
  - lz4-c=1.9.3=h9c3ff4c_1
  - markdown=3.4.1=py38h06a4308_0
  - markupsafe=2.1.1=py38h7f8727e_0
  - matplotlib-base=3.7.1=py38h417a72b_1
  - mkl=2021.4.0=h06a4308_640
  - mkl-service=2.4.0=py38h95df7f1_0
  - mkl_fft=1.3.1=py38h8666266_1
  - mkl_random=1.2.2=py38h1abd341_0
  - moviepy=1.0.3=pyhd8ed1ab_1
  - mpg123=1.31.3=hcb278e6_0
  - msgpack-python=1.0.5=py38hfbd4bf9_0
  - multidict=4.7.6=py38h7b6447c_1
  - munkres=1.1.4=py_0
  - ncurses=6.4=h6a678d5_0
  - nettle=3.6=he412f7d_0
  - networkx=3.1=py38h06a4308_0
  - numba=0.57.1=py38hd559b08_0
  - numexpr=2.8.4=py38he184ba9_0
  - numpy=1.24.3=py38h14f4228_0
  - numpy-base=1.24.3=py38h31eccc5_0
  - oauthlib=3.2.2=py38h06a4308_0
  - olefile=0.46=pyh9f0ad1d_1
  - openh264=2.1.1=h780b84a_0
  - openjpeg=2.4.0=hb52868f_1
  - openssl=1.1.1v=h7f8727e_0
  - pandas=1.5.2=py38h417a72b_0
  - partd=1.2.0=pyhd3eb1b0_1
  - patsy=0.5.3=pyhd8ed1ab_0
  - pcre=8.45=h295c915_0
  - pillow=9.4.0=py38h6a678d5_0
  - pip=23.2.1=py38h06a4308_0
  - platformdirs=3.10.0=pyhd8ed1ab_0
  - pooch=1.7.0=pyha770c72_3
  - proglog=0.1.9=py_0
  - pyasn1=0.4.8=pyhd3eb1b0_0
  - pyasn1-modules=0.2.8=py_0
  - pycparser=2.21=pyhd3eb1b0_0
  - pyjwt=2.4.0=py38h06a4308_0
  - pyopenssl=23.2.0=py38h06a4308_0
  - pyparsing=3.0.9=py38h06a4308_0
  - pyqt=5.9.2=py38h05f1152_4
  - pysocks=1.7.1=pyha2e5f31_6
  - pysoundfile=0.12.1=pyhd8ed1ab_0
  - python=3.8.1=h0371630_1
  - python-dateutil=2.8.2=pyhd3eb1b0_0
  - python-tzdata=2023.3=pyhd8ed1ab_0
  - python_abi=3.8=2_cp38
  - pytorch=1.12.0=py3.8_cuda11.6_cudnn8.3.2_0
  - pytorch-mutex=1.0=cuda
  - pytz=2023.3=pyhd8ed1ab_0
  - pywavelets=1.4.1=py38h5eee18b_0
  - qt=5.9.7=h5867ecd_1
  - re2=2022.04.01=h295c915_0
  - readline=7.0=h7b6447c_5
  - regex=2023.8.8=py38h01eb140_0
  - requests=2.31.0=pyhd8ed1ab_0
  - requests-oauthlib=1.3.0=py_0
  - rsa=4.7.2=pyhd3eb1b0_1
  - scikit-build=0.17.6=pyh4af843d_0
  - scikit-image=0.19.3=py38h6a678d5_1
  - scikit-learn=1.3.0=py38hc099248_0
  - seaborn=0.12.2=hd8ed1ab_0
  - seaborn-base=0.12.2=pyhd8ed1ab_0
  - setuptools=68.0.0=py38h06a4308_0
  - sip=4.19.13=py38h295c915_0
  - six=1.16.0=pyh6c4a22f_0
  - soxr=0.1.3=h0b41bf4_3
  - soxr-python=0.3.5=py38h31356c5_0
  - sqlite=3.33.0=h62c20be_0
  - statsmodels=0.14.0=py38h31356c5_1
  - tensorboard=2.12.1=py38h06a4308_0
  - tensorboard-data-server=0.7.0=py38h52d8a92_0
  - tensorboard-plugin-wit=1.8.1=py38h06a4308_0
  - threadpoolctl=3.2.0=pyha21a80b_0
  - tifffile=2020.10.1=py38hdd07704_2
  - tk=8.6.12=h1ccaba5_0
  - tomli=2.0.1=pyhd8ed1ab_0
  - toolz=0.12.0=py38h06a4308_0
  - torchaudio=0.12.0=py38_cu116
  - torchvision=0.13.0=py38_cu116
  - tornado=6.3.2=py38h5eee18b_0
  - tqdm=4.66.1=pyhd8ed1ab_0
  - typing-extensions=4.7.1=hd8ed1ab_0
  - typing_extensions=4.7.1=pyha770c72_0
  - urllib3=2.0.4=pyhd8ed1ab_0
  - werkzeug=2.2.3=py38h06a4308_0
  - wheel=0.38.4=py38h06a4308_0
  - xz=5.4.2=h5eee18b_0
  - yaml=0.2.5=h7b6447c_0
  - yarl=1.8.1=py38h5eee18b_0
  - zlib=1.2.13=hd590300_5
  - zstd=1.5.2=ha4553b6_0
  - pip:
    - bib-lookup==0.0.24
    - bidict==0.22.1
    - biosppy==1.0.0
    - chardet==5.2.0
    - contourpy==1.1.0
    - cython==3.0.0
    - deprecate-kwargs==0.0.3
    - deprecated==1.2.14
    - einops==0.6.1
    - feedparser==6.0.10
    - fonttools==4.42.0
    - h5py==3.9.0
    - importlib-resources==6.0.1
    - matplotlib==3.7.2
    - opencv-python==********
    - packaging==23.1
    - protobuf==4.24.0
    - pyedflib==0.1.34
    - pytorch-ranger==0.1.1
    - pyyaml==6.0.1
    - scipy==1.10.1
    - sgmllib3k==1.0.0
    - shortuuid==1.0.11
    - tensorboardx==2.6.2
    - torch-ecg==0.0.27
    - torch-optimizer==0.3.0
    - torchsummary==1.5.1
    - wfdb==3.2.0
    - wrapt==1.15.0
    - xmltodict==0.13.0
    - zipp==3.16.2
prefix: /mnt/storage/scratch/gg18045/.conda/envs/ecgpcg-381

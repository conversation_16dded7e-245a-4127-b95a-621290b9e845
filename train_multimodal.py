#!/usr/bin/env python3
"""
多模态ECG-PCG网络训练脚本
使用方法: python train_multimodal.py
"""
import torch
import os
import sys
import argparse
from dataset import ECGPCGDataset
from models.model_mmecgpcgvitnet import ECGPCGViTNet
from models.trainer_multimodal import MultimodalTrainer
import config

def main():
    # 使用config中的参数，但允许覆盖一些关键参数
    args = config.global_opts

    # 简单的参数覆盖（可以通过修改config.py或直接在这里设置）
    # 如果需要不同的参数，可以在这里修改
    training_epochs = 50
    training_lr = 0.0001  # 降低学习率
    training_batch_size = args.batch_size
    
    print("🚀 多模态ECG-PCG网络训练")
    print("="*50)
    
    # 设备检查
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name(0)}")
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    
    print("\n📋 训练参数:")
    print(f"  - 训练轮数 (Epochs): {training_epochs}")
    print(f"  - 批次大小 (Batch Size): {training_batch_size}")
    print(f"  - 学习率 (Learning Rate): {training_lr}")

        # 找到 train_multimodal.py 的 main 函数中的这部分，然后用下面的代码块替换

    # 数据路径设置
    outputpath = config.outputpath

    # 构建数据路径（与main.py保持一致）
    paths_ecgs = [outputpath + f'physionet/data_ecg_{config.global_opts.ecg_type}/']
    paths_pcgs = [outputpath + f'physionet/data_pcg_{config.global_opts.pcg_type}/']
    paths_csvs = [outputpath + f'physionet/data_physionet_raw']  # 不包含.csv后缀

    # 创建数据集
    print("\n📁 加载数据集...")
    dataset = ECGPCGDataset(
        clip_length=config.global_opts.segment_length,
        data_type_ecg="spec", data_type_pcg="spec",  # 🔧 使用频谱图数据
        ecg_sample_rate=config.global_opts.sample_rate_ecg,
        pcg_sample_rate=config.global_opts.sample_rate_pcg,
        verifyComplete=False,
        data_and_label_only=False,
        paths_ecgs=paths_ecgs,
        paths_pcgs=paths_pcgs,
        paths_csv=paths_csvs # 注意这里的变量名是 paths_csvs
    )
    # --- [修改结束] ---
    
    print(f"数据集大小: {len(dataset)}")

    # 测试数据集并获取数据维度
    sample = dataset[0]
    print(f"__getitem__: index: 0, parent_index: None, child_index: None")
    print(sample)

    # 处理不同的数据格式
    if isinstance(sample, dict):
        # 字典格式
        test_ecg_sample = sample['ecg']
        test_pcg_sample = sample['pcg']
        print(f"ECG样本形状: {test_ecg_sample.shape}")
        print(f"PCG样本形状: {test_pcg_sample.shape}")
        x_length = test_ecg_sample.shape[-1] if test_ecg_sample.ndim > 0 else 16000
    else:
        # 元组格式
        (test_ecg_sample, test_pcg_sample), _ = sample
        print(f"ECG样本形状: {test_ecg_sample.shape}")
        print(f"PCG样本形状: {test_pcg_sample.shape}")
        x_length = test_ecg_sample.shape[-1]

    print(f"ECG信号长度: {x_length}")
    
    # 创建模型
    print("\n🧠 创建多模态网络...")
    model = ECGPCGViTNet(
        ecg_input_size=(129, 126),  # 🔧 ECG频谱图尺寸
        pcg_height=128,
        pcg_width=126,  # 🔧 PCG频谱图宽度
        hidden_dim=256,
        num_heads=8,
        num_classes=2,  # 二分类：正常 vs 异常
        dropout=0.1
    )
    
    model = model.to(device)
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 测试模型
    print("\n🔧 测试模型前向传播...")
    test_ecg = torch.randn(2, 1, 129, 126).to(device)  # 🔧 ECG频谱图尺寸
    test_pcg = torch.randn(2, 1, 128, 126).to(device)  # 🔧 PCG频谱图尺寸
    
    with torch.no_grad():
        test_output = model(test_ecg, test_pcg)
        print(f"测试输出形状: {test_output.shape}")
    
    # 恢复训练（如果指定）
    resume_path = args.resume_checkpoint
    if resume_path and os.path.exists(resume_path):
        print(f"\n📂 从 {resume_path} 恢复训练...")
        checkpoint = torch.load(resume_path, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        print("模型权重加载成功")
    
    # 创建训练器
    print("\n⚙️ 初始化训练器...")
    trainer = MultimodalTrainer(
        model=model,
        train_dataset=dataset,  # 会自动分割为训练集和验证集
        val_dataset=None,       # 自动从训练集分割20%作为验证集
        device=device,
        batch_size=training_batch_size,
        learning_rate=training_lr,
        num_epochs=training_epochs,
        val_split=0.2,          # 20%作为验证集
        enable_tensorboard=False,  # 🔧 禁用TensorBoard
        enable_augmentation=True   # 🔥 重新启用强化数据增强 - 对抗过拟合
    )
    
    # 开始训练
    print("\n🎯 开始训练...")
    try:
        trainer.train()
        
        # 保存最终模型
        os.makedirs("results", exist_ok=True)
        model_save_path = "results/multimodal_ecg_pcg_final.pth"
        trainer.save_model(model_save_path)
        
        print(f"\n✅ 训练完成！模型已保存到: {model_save_path}")
        
    except KeyboardInterrupt:
        print("\n⚠️ 训练被用户中断")
        interrupted_save_path = "results/multimodal_ecg_pcg_interrupted.pth"
        trainer.save_model(interrupted_save_path)
        print(f"💾 当前模型已保存到: {interrupted_save_path}")
        
    except Exception as e:
        print(f"\n❌ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        try:
            error_save_path = "results/multimodal_ecg_pcg_error.pth"
            trainer.save_model(error_save_path)
            print(f"💾 错误前的模型已保存到: {error_save_path}")
        except:
            print("无法保存错误前的模型")

if __name__ == "__main__":
    main()
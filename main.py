import torch
# --- this import needed for protobuff issue
from torch import nn, optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
from torch.optim.optimizer import Optimizer
from torch.nn.parallel import DataParallel as DP
from torch.nn.parallel import DistributedDataParallel as DDP  # noqa: F401
from dataset import ECGPCGDataset
from config import outputpath
import json
import numpy as np
import math
import pandas as pd
import os
import numpy as np
from audio import Audio
from pcg import PCG, get_pcg_segments_from_array, save_pcg
from ecg import ECG, save_qrs_inds, get_ecg_segments_from_array, plot_qrs_peaks_and_hr, save_ecg
from spectrograms import Spectrogram
from helpers import get_segment_num, get_filtered_df, create_new_folder, ricker, dataframe_cols, read_signal
import config
import wfdb
import config
from utils import start_logger, stop_logger, initialise_gpu, load_checkpoint, get_summary_writer_log_dir
import sys
import matplotlib as mpl
import matplotlib.pyplot as plt
import torch
from torch_ecg.utils.utils_nn import adjust_cnn_filter_lengths
from torch_ecg.model_configs import ECG_CRNN_CONFIG
from torch_ecg.models.ecg_crnn import ECG_CRNN
from models import TrainCfg, TransformerTrainer
from models.model_mmecgpcgvitnet import ECGPCGViTNet
from models.trainer_multimodal import MultimodalTrainer

def data_sample(outputfolderpath="samples-TEST", dataset="physionet", filename="a0001", index_ephnogram=1, inputpath_data=config.input_physionet_data_folderpath_, inputpath_target=config.input_physionet_target_folderpath_, label=0, \
        transform_type_ecg=config.global_opts.ecg_type, transform_type_pcg=config.global_opts.pcg_type, wavelet_ecg=config.global_opts.cwt_function_ecg, 
        wavelet_pcg=config.global_opts.cwt_function_pcg, window_ecg=None, window_pcg=None, colormap='magma'):
    colormap_suffix = colormap
    if colormap == "jet":
        colormap = plt.cm.jet
    if dataset=="ephnogram":
        sn = 'b0000'[:-len(str(index_ephnogram-1))]+str(index_ephnogram-1)
        ecg = ECG(filename=filename, savename=sn, filepath=inputpath_data, label=label, chan=0, csv_path=inputpath_target, sample_rate=config.global_opts.sample_rate_ecg, normalise=True, apply_filter=True, get_qrs_and_hrs_png=True, save_qrs_hrs_plot=True, outputpath_png=f"{outputfolderpath}/gqrs_peaks/")
        duration = len(ecg.signal)/ecg.sample_rate
        pcg_record = wfdb.rdrecord(inputpath_data+filename, channels=[1])
        audio_sig = np.array(pcg_record.p_signal[:, 0])
        audio = Audio(filename=filename, filepath=inputpath_data, audio=audio_sig, sample_rate=config.base_wfdb_pcg_sample_rate)
        pcg = PCG(filename=filename, savename=sn, audio=audio, sample_rate=config.global_opts.sample_rate_pcg, label=label, normalise=True, apply_filter=True, plot_audio=True, outputpath_png=f"{outputfolderpath}/audio/")
    else: #dataset=="physionet"
        ecg = ECG(filename=filename, filepath=inputpath_data, label=label, csv_path=inputpath_target, sample_rate=config.global_opts.sample_rate_ecg, normalise=True, apply_filter=True, get_qrs_and_hrs_png=True, save_qrs_hrs_plot=True, outputpath_png=f"{outputfolderpath}/gqrs_peaks/")
        duration = len(ecg.signal)/ecg.sample_rate
        audio = Audio(filename=filename, filepath=inputpath_data)
        pcg = PCG(filename=filename, audio=audio, sample_rate=config.global_opts.sample_rate_pcg, label=label, normalise=True, apply_filter=True, plot_audio=True, outputpath_png=f"{outputfolderpath}/audio/")
    seg_num = get_segment_num(ecg.sample_rate, int(len(ecg.signal)), config.global_opts.segment_length, factor=1)      
    ecg_save_name = ecg.filename if ecg.savename == None else ecg.savename
    pcg_save_name = pcg.filename if pcg.savename == None else pcg.savename
    outputpath_ = f"{outputfolderpath}/"
    create_new_folder(f"{outputpath_}data_ecg_{config.global_opts.ecg_type}/{ecg_save_name}/")
    create_new_folder(f"{outputpath_}data_pcg_{config.global_opts.pcg_type}/{pcg_save_name}/")
    save_ecg(ecg_save_name, ecg.signal, ecg.signal_preproc, ecg.qrs_inds, ecg.hrs, outpath=f'{outputpath_}data_ecg_{config.global_opts.ecg_type}/{ecg_save_name}/', type_=config.global_opts.ecg_type)
    save_pcg(pcg_save_name, pcg.signal, pcg.signal_preproc, outpath=f'{outputpath_}data_pcg_{config.global_opts.pcg_type}/{pcg_save_name}/', type_=config.global_opts.pcg_type)
    data = {'filename':ecg_save_name, 'og_filename':filename, 'label':ecg.label, 'record_duration':duration, 'samples_ecg':int(len(ecg.signal)), 'samples_pcg':int(len(pcg.signal)), 'qrs_count':int(len(ecg.qrs_inds)), 'seg_num':seg_num, 'avg_hr':ecg.hr_avg}
    ecg_segments = ecg.get_segments(config.global_opts.segment_length, normalise=ecg.normalise)
    pcg_segments = pcg.get_segments(config.global_opts.segment_length, normalise=pcg.normalise)
    create_new_folder(outputfolderpath)
    spectrogram = Spectrogram(ecg.filename, savename='ecg_'+ecg.filename if ecg.savename == None else ecg.savename+f'_spec_{wavelet_ecg+"_" if transform_type_ecg.startswith("cwt") else ""}_{colormap_suffix}', filepath=outputpath_, sample_rate=config.global_opts.sample_rate_ecg, transform_type=transform_type_ecg,
                                                    signal=ecg.signal, window=window_ecg, window_size=config.spec_win_size_ecg, NFFT=config.global_opts.nfft_ecg, hop_length=config.global_opts.hop_length_ecg, 
                                                    outpath_np=outputpath_+f'/', outpath_png=outputpath_+f'/', 
                                                    normalise=True, start_time=0, wavelet_function=wavelet_ecg, colormap=colormap, save_img=True, show_axis_labels=True, show_legend=True, show_title=True, just_image=False)
    spectrogram_pcg = Spectrogram(filename, savename='pcg_'+filename if sn == None else sn+f'_spec_{wavelet_pcg+"_" if transform_type_pcg.startswith("cwt") else ""}_{colormap_suffix}', filepath=outputpath_, sample_rate=config.global_opts.sample_rate_pcg, transform_type=transform_type_pcg,
                                  signal=pcg.signal, window=window_pcg, window_size=config.spec_win_size_pcg, NFFT=config.global_opts.nfft_pcg, hop_length=config.global_opts.hop_length_pcg, NMels=config.global_opts.nmels,
                                  outpath_np=outputpath_+f'/', outpath_png=outputpath_+f'/', normalise=True, start_time=0, wavelet_function=wavelet_pcg, colormap=colormap, save_img=True, show_axis_labels=True, show_legend=True, show_title=True, just_image=False)
    for index_e, seg in enumerate(ecg_segments):
        seg_spectrogram = Spectrogram(filename, savename='ecg_'+seg.savename+f'_spec_{wavelet_ecg+"_" if transform_type_ecg.startswith("cwt") else ""}{colormap_suffix}', filepath=outputpath_, sample_rate=config.global_opts.sample_rate_ecg, transform_type=transform_type_ecg,
                                            signal=seg.signal, window=window_ecg, window_size=config.spec_win_size_ecg, NFFT=config.global_opts.nfft_ecg, hop_length=config.global_opts.hop_length_ecg, 
                                            outpath_np=outputpath_+f'/', outpath_png=outputpath_+f'/', normalise=True, start_time=seg.start_time, wavelet_function=wavelet_ecg, colormap=colormap, save_img=True, show_axis_labels=True, show_legend=True, show_title=True, just_image=False)
    for index_p, pcg_seg in enumerate(pcg_segments):
        pcg_seg_spectrogram = Spectrogram(filename, savename='pcg_'+pcg_seg.savename+f'_spec_{wavelet_ecg+"_" if transform_type_ecg.startswith("cwt") else ""}_{colormap_suffix}', filepath=outputpath_, sample_rate=config.global_opts.sample_rate_pcg, transform_type=transform_type_pcg,
                                signal=pcg_seg.signal, window=window_pcg, window_size=config.spec_win_size_pcg, NFFT=config.global_opts.nfft_pcg, hop_length=config.global_opts.hop_length_pcg, NMels=config.global_opts.nmels,
                                outpath_np=outputpath_+f'/', outpath_png=outputpath_+f'/', normalise=True, start_time=pcg_seg.start_time, wavelet_function=wavelet_pcg, colormap=colormap, save_img=True, show_axis_labels=True, show_legend=True, show_title=True, just_image=False)
    return data

def main():
    np.random.seed(1)
    device = initialise_gpu(config.global_opts.gpu_id, config.global_opts.enable_gpu)
    torch.cuda.empty_cache()
    #dataset = ECGPCGDataset(clip_length=config.global_opts.segment_length, 
    #                        ecg_sample_rate=config.global_opts.sample_rate_ecg,
    #                        pcg_sample_rate=config.global_opts.sample_rate_pcg,
    #                        verifyComplete=False)
    dataset = ECGPCGDataset(clip_length=config.global_opts.segment_length,
                            data_type_ecg="signal", data_type_pcg="spec",  # 🔧 真正的多模态：ECG用1D信号，PCG用2D频谱图
                            ecg_sample_rate=config.global_opts.sample_rate_ecg,
                            pcg_sample_rate=config.global_opts.sample_rate_pcg,
                            verifyComplete=False,
                            data_and_label_only=False,  # 启用多模态数据
                            paths_ecgs=[outputpath+f'physionet/data_ecg_{config.global_opts.ecg_type}/'],
                            paths_pcgs=[outputpath+f'physionet/data_pcg_{config.global_opts.pcg_type}/'],
                            paths_csv=[outputpath+f'physionet/data_physionet_raw'])
    print(f"Dataset length: {len(dataset)}")
    # 🔧 移除手动数据分割，让训练器自动处理（与 train_multimodal.py 一致）
    print(f"数据集总大小: {len(dataset)} 样本")

    # 🔧 测试数据集格式，确保与 train_multimodal.py 一致
    print("\n🔍 测试数据集格式...")
    sample = dataset[0]
    print(f"样本类型: {type(sample)}")
    if isinstance(sample, dict):
        print(f"ECG形状: {sample['ecg'].shape if hasattr(sample['ecg'], 'shape') else 'N/A'}")
        print(f"PCG形状: {sample['pcg'].shape if hasattr(sample['pcg'], 'shape') else 'N/A'}")
        print(f"标签: {sample['label']}")
    else:
        print(f"样本格式: {sample}")
    print("✅ 数据集格式检查完成\n")
    #normals = 0
    #abnormals = 0
    #for ii in range(data_train.__len__()):
    #    if int(data_train.__getitem__(ii)['label']) == 0:
    #        normals += 1
    #    else:
    #        abnormals +=1
    #sum_normal = normals
    #sum_abnormal = abnormals
    ##abnormal_segs = abnormals['seg_num'].sum()
    ##normal_segs = normals['seg_num'].sum()
    #print(f'(TRAIN) Number of Normal:Abnormal records: {sum_normal}:{sum_abnormal}, Ratio: {sum_normal/max(sum_normal, sum_abnormal)}:{sum_abnormal/max(sum_normal, sum_abnormal)}')
    #normals_test = 0
    #abnormals_test = 0
    #for ii in range(data_test.__len__()):
    #    if int(data_test.__getitem__(ii)['label']) == 0:
    #        normals += 1
    #    else:
    #        abnormals +=1
    #sum_normal_test = normals_test
    #sum_abnormal_test = abnormals_test
    #print(f'(TEST) Number of Normal:Abnormal records: {sum_normal_test}:{sum_abnormal_test}, Ratio: {sum_normal_test/max(sum_normal_test, sum_abnormal_test)}:{sum_abnormal_test/max(sum_normal_test, sum_abnormal_test)}')
    #print(f"FIRST ITEM: {dataset.__getitem__(0, print_short=True)}")
    #print(f"2nd ITEM: {dataset.__getitem__(1, print_short=True)}")
    #print(f"3rd ITEM: {dataset.__getitem__(2, print_short=True)}")
    #test_loader = DataLoader(dataset,
    #                        batch_size=config.global_opts.batch_size,
    #                        shuffle=True,
    #                        num_workers=config.global_opts.n_workers)
    #train_loader = DataLoader(dataset,
    #                        batch_size=config.global_opts.batch_size,
    #                        shuffle=True,
    #                        num_workers=config.global_opts.n_workers)
    #print(f"LENS: TRAIN: {len(train_loader.dataset)}, TEST: {len(test_loader.dataset)}")
    
    classes = ["N", "A"]
    n_leads = 1
    x_length = int(np.floor(config.global_opts.sample_rate_ecg * config.global_opts.segment_length))
        
    # 多模态ECG-PCG Vision Transformer网络
    print("创建多模态ECG-PCG网络...")
    batch_size = config.global_opts.batch_size

    # 🔥 创建强化正则化的多模态网络
    model = ECGPCGViTNet(
        ecg_input_length=x_length,  # 🔧 ECG 1D信号长度
        pcg_height=128,       # PCG频谱图高度
        pcg_width=126,        # 🔧 PCG频谱图宽度
        hidden_dim=256,       # 隐藏层维度
        num_heads=8,          # 注意力头数
        num_classes=len(classes),  # 分类数
        dropout=0.15          # 🔥 适中dropout从0.25降到0.15，避免过度正则化
    )

    print(f"多模态网络参数数量: {sum(p.numel() for p in model.parameters()):,}")
    print(f"可训练参数数量: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")
    
    log_dir = get_summary_writer_log_dir("transformer")
    print(f"Writing logs to {log_dir}")
    logger, ostdout = start_logger(log_dir)
    summary_writer = SummaryWriter(
            str(log_dir),
            flush_secs=5
    )
    
    # 创建必要的目录
    os.makedirs(config.global_opts.checkpoint_path, exist_ok=True)
    os.makedirs(config.global_opts.log_path, exist_ok=True)

    # 如果有预训练模型，加载它
    if config.global_opts.resume_checkpoint is not None:
        print(f"加载预训练模型: {config.global_opts.resume_checkpoint}")
        checkpoint = torch.load(config.global_opts.resume_checkpoint, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        print("预训练模型加载成功")

    # 测试多模态网络前向传播
    print("测试多模态网络...")
    test_ecg = torch.rand(batch_size, 1, x_length)  # 🔧 ECG 1D信号: (batch, channels, length)
    test_pcg = torch.rand(batch_size, 1, 128, 126)  # 🔧 PCG频谱图: (batch, channels, freq, time)
    test_output = model(test_ecg, test_pcg)
    print(f"测试输出形状: {test_output.shape}")
    
    if torch.cuda.device_count() > 1:
        print(f"Using Data Parallelism with {torch.cuda.device_count()} GPUs")
        model = DP(model)
    model.to(device=device)
    
    print("初始化多模态训练器...")
    # 🔧 使用与 train_multimodal.py 相同的优化参数
    trainer = MultimodalTrainer(
        model=model,
        train_dataset=dataset,  # 会自动分割为训练集和验证集
        val_dataset=None,       # 自动从训练集分割20%作为验证集
        device=device,
        batch_size=config.global_opts.batch_size,
        learning_rate=2e-5,     # 🔬 实验B：微调学习率 (0.00002)
        num_epochs=30,          # 🔬 实验A：30轮寻找稳定基线
        val_split=0.2,          # 20%作为验证集
        enable_tensorboard=False,  # 🔧 禁用TensorBoard
        enable_augmentation=True   # 🔥 重新启用强化数据增强 - 对抗过拟合
    )

    try:
        print("开始多模态网络训练...")
        # TensorBoard已禁用，无需显示启动命令
        print("🌐 在浏览器中打开: http://localhost:6006")
        print("-" * 60)

        best_model_state_dict = trainer.train()

        # 保存模型
        model_save_path = os.path.join(config.global_opts.checkpoint_path, "multimodal_ecg_pcg_best.pth")
        trainer.save_model(model_save_path)
        # TensorBoard已禁用，无日志文件

        # 🔧 生成最终的分析报告和可视化
        print("\n" + "="*60)
        print("📊 生成最终分析报告...")
        print("="*60)

        # 绘制最终的 ROC 曲线
        if hasattr(trainer, 'roc_data') and len(trainer.roc_data) > 0:
            print("📈 绘制最终 ROC 曲线...")
            trainer.plot_roc_curve()

            # 打印 AUC 统计
            aucs = [data['auc'] for data in trainer.roc_data]
            best_auc = max(aucs)
            final_auc = aucs[-1]
            print(f"🎯 最佳 AUC: {best_auc:.4f}")
            print(f"🎯 最终 AUC: {final_auc:.4f}")

        # 打印训练统计摘要
        print(f"\n📋 训练摘要:")
        print(f"   总训练轮数: {len(trainer.train_losses)}")
        print(f"   最佳验证准确率: {max(trainer.val_accuracies):.4f} ({max(trainer.val_accuracies)*100:.2f}%)")
        print(f"   最终验证准确率: {trainer.val_accuracies[-1]:.4f} ({trainer.val_accuracies[-1]*100:.2f}%)")
        if hasattr(trainer, 'roc_data') and len(trainer.roc_data) > 0:
            print(f"   最佳 AUC 分数: {max(aucs):.4f}")
            print(f"   最终 AUC 分数: {aucs[-1]:.4f}")

        print(f"\n📁 生成的文件:")
        print(f"   模型文件: {model_save_path}")
        print(f"   训练曲线: {trainer.results_dir}/realtime_training_curves.png")
        print(f"   混淆矩阵: {trainer.results_dir}/latest_confusion_matrix.png")
        if hasattr(trainer, 'roc_data') and len(trainer.roc_data) > 0:
            print(f"   ROC 曲线: {trainer.results_dir}/roc_curves.png")
            print(f"   实时 ROC: {trainer.results_dir}/realtime_roc_curve.png")

        print(f"\n** 🎉 多模态训练完成！最佳模型已保存到: {model_save_path} **")

    except KeyboardInterrupt:
        print("训练被用户中断")
        try:
            sys.exit(0)
        except SystemExit:
            os._exit(0)
            
    summary_writer.close()
    stop_logger(logger, ostdout)
    
    
    # *** UNUSED MMECGPCGNET CODE ***
    #
    #   abstractmethods (_setup_dataloaders, run_one_step, evaluate, batch_dim, etc.)
        
    #   def __getitem__(self, index:int) -> Tuple[np.ndarray, ...]:
    #      return self._all_data[index], self._all_labels[index], self._all_masks[index]
    #   evaluate(self, data_loader:DataLoader) -> Dict[str, float]
        
    #   model = ECGPCGVisNet()
    #   loss_f = nn.CrossEntropyLoss()
    #   criterion = loss_f  #lambda logits, labels: torch.tensor(0)
    #   optimizer = optim.SGD(model.parameters(), lr=config.global_opts.learning_rate, momentum=config.global_opts.sgd_momentum)
    #   if config.global_opts.opt_adam:
    #       optimizer = optim.Adam(model.parameters(), lr=config.global_opts.learning_rate, betas=(config.global_opts.sgd_momentum, 0.999), eps=1e-08, weight_decay=config.global_opts.adam_weight_decay, amsgrad=config.global_opts.adam_amsgrad)
    #   trainer = ECGPCGVisTrainer(
    #        model, train_loader, test_loader, criterion, optimizer, summary_writer, device
    #   )
    #   trainer.train(
    #       config.global_opts.epochs,
    #       config.global_opts.val_frequency,
    #       print_frequency=config.global_opts.print_frequency,
    #       log_frequency=config.global_opts.log_frequency,
    #   )
    #   model.eval()
    #   with torch.no_grad():  
    #   trainer.eval(train_loader, test_loader)

if __name__ == '__main__':
    mpl.rcParams['agg.path.chunksize'] = 10000
    print(f'**** main started - creating sample data, visualisations and launching model ****\n')
    
    create_new_folder(config.outputpath+"results")
    create_new_folder("samples")
    
    # Delete all files in directory with pattern file.endswith("_spec_cwt_spec.npz")
    #dirs_ecg = next(os.walk(outputpath+f'ephnogram/data_ecg_{config.global_opts.ecg_type}/'))[1]
    #for dir in dirs_ecg:
    #    dirs_inner = next(os.walk(outputpath+f'ephnogram/data_ecg_{config.global_opts.ecg_type}/'+f'{dir}/'))[1]
    #    for j, d in enumerate(dirs_inner):
    #        files_ecg = next(os.walk(outputpath+f'ephnogram/data_ecg_{config.global_opts.ecg_type}/'+f'{dir}/{d}/'))[2]
    #        files_pcg = next(os.walk(outputpath+f'ephnogram/data_pcg_{config.global_opts.pcg_type}/'+f'{dir}/{d}/'))[2]
    #        valid_files_ecg = [f for f in files_ecg if f.endswith("_spec_cwt_spec.npz")]
    #        valid_files_pcg = [f for f in files_pcg if f.endswith("_spec_cwt_spec.npz")]
    #        for v in valid_files_ecg:
    #            os.remove(outputpath+f'ephnogram/data_ecg_{config.global_opts.ecg_type}/'+f'{dir}/{d}/'+v)
    #        for v in valid_files_pcg:
    #            os.remove(outputpath+f'ephnogram/data_pcg_{config.global_opts.pcg_type}/'+f'{dir}/{d}/'+v)
    
    # Samples in the paper
        #data_sample(filename="a0001", outputfolderpath="samples/a0001", label=0, transform_type_ecg="stft", transform_type_pcg="stft_logmel", colormap="magma", inputpath_data=config.input_physionet_data_folderpath_, inputpath_target=config.input_physionet_target_folderpath_)
        #data_sample(filename="a0007", outputfolderpath="samples/a0007", label=0, transform_type_ecg="cwt", transform_type_pcg="cwt", wavelet_ecg="ricker", wavelet_pcg="ricker", colormap="magma", inputpath_data=config.input_physionet_data_folderpath_, inputpath_target=config.input_physionet_target_folderpath_)
        #data_sample(filename="ECGPCG0003", outputfolderpath="samples/b0001", label=0, transform_type_ecg="cwt", index_ephnogram=1, transform_type_pcg="cwt", wavelet_ecg="morlet", wavelet_pcg="morlet", colormap="magma", dataset="ephnogram", inputpath_data=config.input_ephnogram_data_folderpath_, inputpath_target=config.input_ephnogram_target_folderpath_)
        #data_sample(filename="a0001", outputfolderpath="samples/a0001", label=0, transform_type_ecg="stft_log", transform_type_pcg="stft_mel", colormap="magma", inputpath_data=config.input_physionet_data_folderpath_, inputpath_target=config.input_physionet_target_folderpath_)
        #data_sample(filename="a0001", outputfolderpath="samples/a0001", label=0, transform_type_ecg="cwt", transform_type_pcg="cwt", colormap="magma", wavelet_ecg="ricker", wavelet_pcg="ricker", inputpath_data=config.input_physionet_data_folderpath_, inputpath_target=config.input_physionet_target_folderpath_)
        #data_sample(filename="a0001", outputfolderpath="samples/a0001", label=0, transform_type_ecg="cwt", transform_type_pcg="cwt", colormap="magma", wavelet_ecg="morlet", wavelet_pcg="morlet", inputpath_data=config.input_physionet_data_folderpath_, inputpath_target=config.input_physionet_target_folderpath_)
    
    # UNUSED sample examples
        #data_sample(filename="a0315", outputfolderpath="samples/a0315", label=1)
        #data_sample(filename="a0007", outputfolderpath="samples/a0007", label=1)
        #data_sample(filename="ECGPCG0003", index_ephnogram=1, outputfolderpath="samples/b0001", dataset="ephnogram", inputpath_data=config.input_ephnogram_data_folderpath_, inputpath_target=config.input_ephnogram_target_folderpath_, label=0)
        #data_sample(wavelet_ecg="ricker", wavelet_pcg="morlet", colormap="magma")
        #data_sample(wavelet_ecg="ricker", wavelet_pcg="ricker", colormap="magma")
        #data_sample(outputfolderpath="samples-TEST/stft", transform_type_ecg="stft_log", transform_type_pcg="stft_log", colormap="magma")
        #data_sample(outputfolderpath="samples-TEST/stft", transform_type_ecg="stft_log", transform_type_pcg="stft_log", colormap="jet")
        #data_sample(outputfolderpath="samples-TEST/stft_logmel", transform_type_ecg="stft_log", transform_type_pcg="stft_logmel")
    
    torch.backends.cudnn.benchmark = config.global_opts.enable_gpu

    # Model training
    main()
  
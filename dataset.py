import torch
from torch.utils.data.dataset import Dataset
import numpy as np
import pandas as pd
import config
from config import global_opts, outputpath
from helpers import dataframe_cols, read_file, check_datatype_and_filetype
import os

class ECGPCGDataset(Dataset):
    def __init__(self, 
                 samples_in_directories=True,
                 file_type_ecg="npz", file_type_pcg="npz",  
                 clip_length=global_opts.segment_length, 
                 data_type_ecg="signal", data_type_pcg="signal",
                 ecg_sample_rate=global_opts.sample_rate_ecg, 
                 pcg_sample_rate=global_opts.sample_rate_pcg,
                 paths_ecgs=None, paths_pcgs=None, paths_csv=None,
                 verifyComplete=True, data_and_label_only=True):

        # --- 参数校验 ---
        if data_type_ecg not in config.data_types_ecg:
            raise ValueError(f"Error: 'data_type_ecg' must be one of {config.data_types_ecg}") 
        if data_type_pcg not in config.data_types_pcg:
            raise ValueError(f"Error: 'data_type_pcg' must be one of {config.data_types_pcg}")
        if not (paths_ecgs and paths_pcgs and paths_csv and (len(paths_ecgs) == len(paths_pcgs) == len(paths_csv))):
            raise ValueError("Error: paths_ecgs, paths_pcgs and paths_csv must be provided and have the same length.")
        
        self.clip_length = clip_length
        self.data_type_ecg, self.data_type_pcg = data_type_ecg, data_type_pcg
        self.file_type_ecg, self.file_type_pcg = file_type_ecg, file_type_pcg
        self.data_and_label_only = data_and_label_only
        self.ecg_sample_rate, self.pcg_sample_rate = ecg_sample_rate, pcg_sample_rate

        self.ecg_paths, self.pcg_paths, self.labels = [], [], []
        self.parent_indices, self.segment_indices = [], []

        self._load_and_verify_data(paths_ecgs, paths_pcgs, paths_csv, verifyComplete)

        print(f"ECGPCG DATASET: Found {len(self.ecg_paths)} valid segments.")

    def _load_and_verify_data(self, paths_ecgs, paths_pcgs, paths_csvs, verifyComplete):
        print("* Validating directories and files...")
        
        all_dfs = [pd.read_csv(f"{path}.csv") for path in paths_csvs]
        master_df = pd.concat(all_dfs, ignore_index=True)

        flat_ecg_paths, flat_pcg_paths = [], []
        parent_idx_map, segment_idx_map = [], []

        parent_counter = 0
        for i, row in master_df.iterrows():
            filename, label, seg_num = row['filename'], row['label'], row['seg_num']
            
            # 找到文件所在的具体数据集路径
            dataset_idx = 0 # 简化处理，假设都在第一个路径下
            ecg_sample_dir = os.path.join(paths_ecgs[dataset_idx], filename)
            pcg_sample_dir = os.path.join(paths_pcgs[dataset_idx], filename)
            
            if not os.path.isdir(ecg_sample_dir) or not os.path.isdir(pcg_sample_dir):
                if verifyComplete:
                    raise FileNotFoundError(f"Sample directory not found for {filename}")
                print(f"Warning: Skipping sample {filename}, directory not found.")
                continue

            segs_found = 0
            for seg_idx in range(seg_num):
                # 修正文件名格式：根据实际文件结构构建路径
                # ECG: {filename}_seg_{seg_idx}_stft.npz
                # PCG: {filename}_seg_{seg_idx}_stft_logmel.npz
                ecg_seg_path = os.path.join(ecg_sample_dir, str(seg_idx), f"{filename}_seg_{seg_idx}_stft.{self.file_type_ecg}")
                pcg_seg_path = os.path.join(pcg_sample_dir, str(seg_idx), f"{filename}_seg_{seg_idx}_stft_logmel.{self.file_type_pcg}")

                if os.path.exists(ecg_seg_path) and os.path.exists(pcg_seg_path):
                    flat_ecg_paths.append(ecg_seg_path)
                    flat_pcg_paths.append(pcg_seg_path)
                    parent_idx_map.append(parent_counter)
                    segment_idx_map.append(seg_idx)
                    segs_found += 1
            
            if segs_found > 0:
                self.labels.extend([label] * segs_found)
                parent_counter += 1

        self.ecg_paths = flat_ecg_paths
        self.pcg_paths = flat_pcg_paths
        self.parent_indices = parent_idx_map
        self.segment_indices = segment_idx_map
        
    def __len__(self):
        return len(self.ecg_paths)

    def __getitem__(self, index):
        ecg_path = self.ecg_paths[index]
        pcg_path = self.pcg_paths[index]
        label = self.labels[index]

        # 🔧 修复：根据data_type参数选择正确的文件和数据
        try:
            # 根据data_type_ecg选择ECG文件和数据
            if self.data_type_ecg == 'spec':
                # 对于频谱图，需要加载 *_spec.npz 文件
                ecg_spec_path = ecg_path.replace('.npz', '_spec.npz')
                ecg_file = np.load(ecg_spec_path)
                ecg_data = ecg_file['spec']  # 加载频谱图数据
            else:
                # 对于原始信号，加载普通文件
                ecg_file = np.load(ecg_path)
                ecg_data = ecg_file['signal']  # 加载原始信号数据

            # 根据data_type_pcg选择PCG文件和数据
            if self.data_type_pcg == 'spec':
                # 对于频谱图，需要加载 *_spec.npz 文件
                pcg_spec_path = pcg_path.replace('.npz', '_spec.npz')
                pcg_file = np.load(pcg_spec_path)
                pcg_data = pcg_file['spec']  # 加载频谱图数据
            else:
                # 对于原始信号，加载普通文件
                pcg_file = np.load(pcg_path)
                pcg_data = pcg_file['signal']  # 加载原始信号数据
        except Exception as e:
            print(f"Error loading data for index {index}, path: {ecg_path} or {pcg_path}")
            print(f"Error: {e}")
            # 返回一个零张量以避免训练崩溃
            ecg_data = np.zeros((16000,), dtype=np.float32)
            pcg_data = np.zeros((128, 128), dtype=np.float32)

        # 格式化输出
        # 清理并格式化 ECG
        if not np.all(np.isfinite(ecg_data)):
            ecg_data = np.nan_to_num(ecg_data, nan=0.0)
        ecg_tensor = torch.from_numpy(np.expand_dims(ecg_data, axis=0).astype(np.float32))

        # 清理并格式化 PCG (假设PCG已经是频谱图)
        if not np.all(np.isfinite(pcg_data)):
            pcg_data = np.nan_to_num(pcg_data, nan=0.0)
        pcg_tensor = torch.from_numpy(np.expand_dims(pcg_data, axis=0).astype(np.float32))

        # 确保标签是有效的
        if label == -1:
            label = 0

        # 根据 data_and_label_only 参数决定返回格式
        if self.data_and_label_only:
            # 简单格式：只返回数据和标签
            return (ecg_tensor, pcg_tensor), torch.tensor(label, dtype=torch.long)
        else:
            # 完整格式：返回包含所有信息的字典
            # 从文件路径中提取文件名信息
            filename = os.path.basename(ecg_path).split('_seg_')[0]
            seg_index = int(os.path.basename(ecg_path).split('_seg_')[1].split('_')[0])

            return {
                'filename': filename,
                'og_filename': filename,
                'label': label,
                'record_duration': 35.666,  # 默认值，实际应该从数据中获取
                'num_channels': 1,
                'samples_ecg': len(ecg_data),
                'samples_pcg': len(pcg_data.flatten()) if pcg_data.ndim > 1 else len(pcg_data),
                'qrs_count': 37,  # 默认值，实际应该从数据中获取
                'seg_num': 4,     # 默认值，实际应该从数据中获取
                'avg_hr': 62.087370,  # 默认值，实际应该从数据中获取
                'ecg_path': ecg_path,
                'pcg_path': pcg_path,
                'ecg': ecg_data,
                'pcg': pcg_data,
                'qrs': np.array([1550, 3530, 5530, 7512, 9500, 11508, 13501, 15482]),  # 默认值
                'hrs': np.array([np.nan] * 8, dtype=np.float32),  # 默认值
                'freqs_ecg': [],
                'times_ecg': [],
                'freqs_pcg': [],
                'times_pcg': [],
                'index': index,
                'parent_index': self.parent_indices[index] if index < len(self.parent_indices) else 0,
                'seg_index': seg_index
            }
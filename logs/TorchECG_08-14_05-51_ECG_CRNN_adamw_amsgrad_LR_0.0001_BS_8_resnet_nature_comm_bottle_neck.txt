2023-08-14 05:51:41,475 - TorchECG - INFO - training configurations are as follows:
{
    "debug": True,
    "final_model_name": None,
    "log_step": 20,
    "flooding_level": 0.0,
    "early_stopping": {
        "min_delta": 0.001,
        "patience": 10
    },
    "log_dir": "logs",
    "checkpoints": checkpoints,
    "model_dir": None,
    "working_dir": "",
    "prefix": "TorchECG",
    "DTYPE": DTYPE(STR='float32', NP=dtype('float32'), TORCH=torch.float32, INT=32),
    "str_dtype": "float32",
    "np_dtype": float32,
    "dtype": torch.float32,
    "device": cpu,
    "eps": 1e-07,
    "SEED": 42,
    "RNG": Generator(PCG64),
    "RNG_sample": functools.partial(<built-in method choice of numpy.random._generator.Generator object at 0x2b545dc8c900>, replace=False, shuffle=False),
    "RNG_randint": functools.partial(<built-in method integers of numpy.random._generator.Generator object at 0x2b545dc8c900>, endpoint=True),
    "set_seed": <function set_seed at 0x2b545dc94a60>,
    "change_dtype": <function change_dtype at 0x2b545dca3040>,
    "classes": [
        "N",
        "A"
    ],
    "n_epochs": 100,
    "batch_size": 8,
    "optimizer": "adamw_amsgrad",
    "momentum": 0.949,
    "betas": [
        0.9, 0.999
    ],
    "decay": 0.01,
    "learning_rate": 0.0001,
    "lr": 0.0001,
    "lr_scheduler": "one_cycle",
    "lr_step_size": 50,
    "lr_gamma": 0.1,
    "max_lr": 0.002,
    "burn_in": 400,
    "steps": [
        5000, 10000
    ],
    "loss": "AsymmetricLoss",
    "loss_kw": {
        "gamma_pos": 0,
        "gamma_neg": 0.2,
        "implementation": "deep-psp"
    },
    "monitor": "challenge_metric",
    "eval_every": 20,
    "cnn_name": "transformer",
    "rnn_name": "none",
    "attn_name": "none",
    "input_len": 16000,
    "input_len_tol": 3200,
    "sig_slice_tol": 0.4,
    "siglen": 16000,
    "physionetOnly": False
}
2023-08-14 05:51:42,674 - TorchECG - INFO - training configurations are as follows:
{
    "debug": True,
    "final_model_name": None,
    "log_step": 20,
    "flooding_level": 0.0,
    "early_stopping": {
        "min_delta": 0.001,
        "patience": 10
    },
    "log_dir": "logs",
    "checkpoints": checkpoints,
    "model_dir": None,
    "working_dir": "",
    "prefix": "TorchECG",
    "DTYPE": DTYPE(STR='float32', NP=dtype('float32'), TORCH=torch.float32, INT=32),
    "str_dtype": "float32",
    "np_dtype": float32,
    "dtype": torch.float32,
    "device": cpu,
    "eps": 1e-07,
    "SEED": 42,
    "RNG": Generator(PCG64),
    "RNG_sample": functools.partial(<built-in method choice of numpy.random._generator.Generator object at 0x2b545dc8c900>, replace=False, shuffle=False),
    "RNG_randint": functools.partial(<built-in method integers of numpy.random._generator.Generator object at 0x2b545dc8c900>, endpoint=True),
    "set_seed": <function set_seed at 0x2b545dc94a60>,
    "change_dtype": <function change_dtype at 0x2b545dca3040>,
    "classes": [
        "N",
        "A"
    ],
    "n_epochs": 100,
    "batch_size": 8,
    "optimizer": "adamw_amsgrad",
    "momentum": 0.949,
    "betas": [
        0.9, 0.999
    ],
    "decay": 0.01,
    "learning_rate": 0.0001,
    "lr": 0.0001,
    "lr_scheduler": "one_cycle",
    "lr_step_size": 50,
    "lr_gamma": 0.1,
    "max_lr": 0.002,
    "burn_in": 400,
    "steps": [
        5000, 10000
    ],
    "loss": "AsymmetricLoss",
    "loss_kw": {
        "gamma_pos": 0,
        "gamma_neg": 0.2,
        "implementation": "deep-psp"
    },
    "monitor": "challenge_metric",
    "eval_every": 20,
    "cnn_name": "transformer",
    "rnn_name": "none",
    "attn_name": "none",
    "input_len": 16000,
    "input_len_tol": 3200,
    "sig_slice_tol": 0.4,
    "siglen": 16000,
    "physionetOnly": True
}
2023-08-14 05:51:44,386 - TorchECG - INFO - 
Starting training:
------------------
Epochs:          100
Batch size:      8
Learning rate:   0.0001
Training size:   1962
Validation size: 841
Device:          cpu
Optimizer:       adamw_amsgrad
Dataset classes: ['N', 'A']
-----------------------------------------

2023-08-14 05:51:44,390 - TorchECG - INFO - Train epoch_0:
--------------------------------------------------------------------------------------------------------------
2023-08-14 05:52:15,182 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 20: train/loss : 0.2356
Epoch 0 / Step 20: train/lr :   0.0001
--------------------------------------------------
2023-08-14 05:52:42,653 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 40: train/loss : 0.9756
Epoch 0 / Step 40: train/lr :   0.0001
--------------------------------------------------
2023-08-14 05:53:10,005 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 60: train/loss : 0.6498
Epoch 0 / Step 60: train/lr :   0.0001
--------------------------------------------------
2023-08-14 05:53:37,729 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 80: train/loss : 0.4805
Epoch 0 / Step 80: train/lr :   0.0001
--------------------------------------------------
2023-08-14 05:54:05,510 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 100: train/loss : 0.6889
Epoch 0 / Step 100: train/lr :   0.0001
--------------------------------------------------
2023-08-14 05:54:32,904 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 120: train/loss : 0.4840
Epoch 0 / Step 120: train/lr :   0.0001
--------------------------------------------------
2023-08-14 05:55:00,932 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 140: train/loss : 0.4401
Epoch 0 / Step 140: train/lr :   0.0001
--------------------------------------------------
2023-08-14 05:55:27,894 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 160: train/loss : 0.3069
Epoch 0 / Step 160: train/lr :   0.0001
--------------------------------------------------
2023-08-14 05:55:55,734 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 180: train/loss : 0.5086
Epoch 0 / Step 180: train/lr :   0.0001
--------------------------------------------------
2023-08-14 05:56:16,678 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 200: train/loss : 0.2412
Epoch 0 / Step 200: train/lr :   0.0001
--------------------------------------------------
2023-08-14 05:56:36,338 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 220: train/loss : 0.1058
Epoch 0 / Step 220: train/lr :   0.0001
--------------------------------------------------
2023-08-14 05:56:54,756 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 240: train/loss : 0.2058
Epoch 0 / Step 240: train/lr :   0.0001
--------------------------------------------------
2023-08-14 05:58:33,042 - TorchECG - DEBUG - all_scalar_preds.shape = (1962, 2), all_labels.shape = (1962, 1)
2023-08-14 05:58:33,044 - TorchECG - INFO - 
----------------------------------------------
scalar prediction:    [0.498, 0.502]
binary prediction:    [1]
labels:               [1]
predicted classes:    [1]
label classes:        [1]
----------------------------------------------

2023-08-14 05:58:33,044 - TorchECG - INFO - 
----------------------------------------------
scalar prediction:    [0.479, 0.521]
binary prediction:    [1]
labels:               [1]
predicted classes:    [1]
label classes:        [1]
----------------------------------------------

2023-08-14 05:58:38,376 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 246: train/auroc :            nan
Epoch 0 / Step 246: train/auprc :            0.5052
Epoch 0 / Step 246: train/accuracy :         0.6723
Epoch 0 / Step 246: train/f_measure :        0.3775
Epoch 0 / Step 246: train/f_beta_measure :   0.3025
Epoch 0 / Step 246: train/g_beta_measure :   0.1420
Epoch 0 / Step 246: train/challenge_metric : 0.2671
--------------------------------------------------

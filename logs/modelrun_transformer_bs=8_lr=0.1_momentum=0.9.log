测试多模态网络...
测试输出形状: torch.Size([8, 2])
初始化多模态训练器...
数据分割完成: 训练集 1148 样本, 验证集 287 样本
================================================================================
🚀 多模态ECG-PCG网络训练日志
开始时间: 2025-07-02 21:19:07
TensorBoard: 已禁用
================================================================================

🔥 开始冻结预训练层...
   ❄️ 冻结PCG编码器layer1
   ❄️ 冻结PCG编码器layer2
🔥 预训练层冻结完成:
   📊 总参数: 24,703,234
   🔥 可训练参数: 23,361,602 (94.6%)
   ❄️ 冻结参数: 1,341,632 (5.4%)

🔥 设置差分学习率...
   🔥 预训练层学习率: 0.000002 (60个参数组)
   🔥 新层学习率: 0.000020 (93个参数组)
🔥 差分学习率设置完成: 2个参数组
🔬 实验B配置 - 类别权重: 正常类权重=2.5, 异常类权重=1.0
   🎯 目标: 在稳定基线上优化性能
   🚀 微调学习率 + 标准权重衰减
   ⚡ 准备逐步引入数据增强
🔥 启用适中数据增强 - 平衡正则化和学习:
   📊 SpecAugment: 频率遮挡20点, 时间遮挡20点, 概率50%
   🫀 ECG增强: 噪声+偏移各50%概率, SNR 15-25dB, 偏移5%
   🎯 Mixup: alpha=0.2, 概率40% - 平衡策略
   📈 幅度缩放: 概率30%, 范围80%-120%
   🚀 目标: 适度正则化，保持模型学习能力
🔥 优化Focal Loss备选方案: alpha=0.8, gamma=2.0
   📊 当前使用: Weighted CrossEntropy
   🔧 Focal权重: [0.8 0.2]
   🎯 备选策略: 当权重增加导致训练不稳定时自动切换
   ⚡ 动态切换: 可根据训练稳定性调整
🔬 实验B - 基线优化配置:
   📊 微调学习率: 0.000020
   ⚡ 权重衰减: 1e-4 (标准正则化)
   🎯 目标: 在稳定基线上提升性能
🔥 启用强化早停机制:
   📊 监控指标: Macro F1-Score (更适合不平衡数据)
   ⏰ 耐心值: 20 epochs
   📈 最小改善: 0.001
   🎯 目标: 防止训练后期性能下降，节省时间
🎯 启用决策阈值调整: 默认阈值=0.5
开始多模态网络训练...
🌐 在浏览器中打开: http://localhost:6006
------------------------------------------------------------

📋 训练配置:
   总epoch数: 30
   训练集大小: 1148
   验证集大小: 287
   批次大小: 8
   学习率: 2e-05
   设备: cuda:0
   模型参数数量: 24,703,234

   🗑️ 已删除旧图片: realtime_training_curves.png
   🗑️ 已删除旧图片: realtime_training_curves_english.png
   🗑️ 已删除旧图片: latest_confusion_matrix.png
   🗑️ 已删除旧图片: best_confusion_matrix.png

============================================================
🚀 EPOCH 1/30 开始训练
   开始时间: 21:19:07
============================================================
🔍 批次 0 数据形状调试:
   原始样本类型: <class 'dict'>
   样本ECG形状: torch.Size([8, 16000])
   样本PCG形状: torch.Size([8, 128, 126])
   提取后ECG形状: torch.Size([8, 1, 16000])
   提取后PCG形状: torch.Size([8, 1, 128, 126])
   提取后标签形状: torch.Size([8])
🔍 开始验证epoch 1...
验证集批次数量: 36
验证数据设备检查:
  ECG设备: cuda:0, 形状: torch.Size([8, 1, 16000])
  PCG设备: cuda:0, 形状: torch.Size([8, 1, 128, 126])
  标签设备: cuda:0, 形状: torch.Size([8])
📊 AUC Score: 0.5366
验证完成: 处理了 287 个样本
🔬 固定学习率: 0.000002 (微调版本)

📊 EPOCH 1 结果:
   🚂 训练集:
      损失: 0.8524
      准确率: 0.4730 (47.30%)
      精确率: 0.6039
      召回率: 0.4730
      F1分数: 0.4976
   🔍 验证集:
      损失: 0.7182
      准确率: 0.5052 (50.52%)
      精确率: 0.6343
      召回率: 0.5052
      F1分数: 0.5253
   ⚙️ 其他:
      学习率: 0.000002
      耗时: 19.8秒
   🎉 Macro F1-Score提升: 0.4959 (新最佳)
   🎉 新的最佳验证准确率: 0.5052 (50.52%)

🔍 Confusion Matrix Analysis:
   📊 Training Confusion Matrix Metrics:
      True Positives (TP): 361
      True Negatives (TN): 182
      False Positives (FP): 135
      False Negatives (FN): 470
      Accuracy: 0.4730 (47.30%)
      Precision: 0.7278 (72.78%)
      Recall (Sensitivity): 0.4344 (43.44%)
      Specificity: 0.5741 (57.41%)
      F1-Score: 0.5441
   📊 Validation Confusion Matrix Metrics:
      True Positives (TP): 92
      True Negatives (TN): 53
      False Positives (FP): 29
      False Negatives (FN): 113
      Accuracy: 0.5052 (50.52%)
      Precision: 0.7603 (76.03%)
      Recall (Sensitivity): 0.4488 (44.88%)
      Specificity: 0.6463 (64.63%)
      F1-Score: 0.5644

📈 Train vs Validation Comparison:
   Accuracy Gap: 3.22%
   Precision Gap: 3.25%
   Recall Gap: 1.44%
📊 ROC 曲线已保存到: results/roc_curves.png

============================================================
🚀 EPOCH 2/30 开始训练
   开始时间: 21:19:33
============================================================
🔍 批次 0 数据形状调试:
   原始样本类型: <class 'dict'>
   样本ECG形状: torch.Size([8, 16000])
   样本PCG形状: torch.Size([8, 128, 126])
   提取后ECG形状: torch.Size([8, 1, 16000])
   提取后PCG形状: torch.Size([8, 1, 128, 126])
   提取后标签形状: torch.Size([8])
🔍 开始验证epoch 2...
验证集批次数量: 36
验证数据设备检查:
  ECG设备: cuda:0, 形状: torch.Size([8, 1, 16000])
  PCG设备: cuda:0, 形状: torch.Size([8, 1, 128, 126])
  标签设备: cuda:0, 形状: torch.Size([8])
📊 AUC Score: 0.6115
验证完成: 处理了 287 个样本
🔬 固定学习率: 0.000002 (微调版本)

📊 EPOCH 2 结果:
   🚂 训练集:
      损失: 0.7579
      准确率: 0.5296 (52.96%)
      精确率: 0.6344
      召回率: 0.5296
      F1分数: 0.5543
   🔍 验证集:
      损失: 0.6854
      准确率: 0.5366 (53.66%)
      精确率: 0.6478
      召回率: 0.5366
      F1分数: 0.5578
   ⚙️ 其他:
      学习率: 0.000002
      耗时: 13.4秒
   🎉 Macro F1-Score提升: 0.5221 (新最佳)
   🎉 新的最佳验证准确率: 0.5366 (53.66%)

🔍 Confusion Matrix Analysis:
   📊 Training Confusion Matrix Metrics:
      True Positives (TP): 427
      True Negatives (TN): 181
      False Positives (FP): 136
      False Negatives (FN): 404
      Accuracy: 0.5296 (52.96%)
      Precision: 0.7584 (75.84%)
      Recall (Sensitivity): 0.5138 (51.38%)
      Specificity: 0.5710 (57.10%)
      F1-Score: 0.6126
   📊 Validation Confusion Matrix Metrics:
      True Positives (TP): 102
      True Negatives (TN): 52
      False Positives (FP): 30
      False Negatives (FN): 103
      Accuracy: 0.5366 (53.66%)
      Precision: 0.7727 (77.27%)
      Recall (Sensitivity): 0.4976 (49.76%)
      Specificity: 0.6341 (63.41%)
      F1-Score: 0.6053

📈 Train vs Validation Comparison:
   Accuracy Gap: 0.70%
   Precision Gap: 1.43%
   Recall Gap: 1.63%
📊 ROC 曲线已保存到: results/roc_curves.png

============================================================
🚀 EPOCH 3/30 开始训练
   开始时间: 21:19:53
============================================================
🔍 批次 0 数据形状调试:
   原始样本类型: <class 'dict'>
   样本ECG形状: torch.Size([8, 16000])
   样本PCG形状: torch.Size([8, 128, 126])
   提取后ECG形状: torch.Size([8, 1, 16000])
   提取后PCG形状: torch.Size([8, 1, 128, 126])
   提取后标签形状: torch.Size([8])
🔍 开始验证epoch 3...
验证集批次数量: 36
验证数据设备检查:
  ECG设备: cuda:0, 形状: torch.Size([8, 1, 16000])
  PCG设备: cuda:0, 形状: torch.Size([8, 1, 128, 126])
  标签设备: cuda:0, 形状: torch.Size([8])
📊 AUC Score: 0.6566
验证完成: 处理了 287 个样本
🔬 固定学习率: 0.000002 (微调版本)

📊 EPOCH 3 结果:
   🚂 训练集:
      损失: 0.7382
      准确率: 0.5505 (55.05%)
      精确率: 0.6387
      召回率: 0.5505
      F1分数: 0.5742
   🔍 验证集:
      损失: 0.6621
      准确率: 0.5714 (57.14%)
      精确率: 0.6729
      召回率: 0.5714
      F1分数: 0.5917
   ⚙️ 其他:
      学习率: 0.000002
      耗时: 11.2秒
   🎉 Macro F1-Score提升: 0.5545 (新最佳)
   🎉 新的最佳验证准确率: 0.5714 (57.14%)

🔍 Confusion Matrix Analysis:
   📊 Training Confusion Matrix Metrics:
      True Positives (TP): 459
      True Negatives (TN): 173
      False Positives (FP): 144
      False Negatives (FN): 372
      Accuracy: 0.5505 (55.05%)
      Precision: 0.7612 (76.12%)
      Recall (Sensitivity): 0.5523 (55.23%)
      Specificity: 0.5457 (54.57%)
      F1-Score: 0.6402
   📊 Validation Confusion Matrix Metrics:
      True Positives (TP): 110
      True Negatives (TN): 54
      False Positives (FP): 28
      False Negatives (FN): 95
      Accuracy: 0.5714 (57.14%)
      Precision: 0.7971 (79.71%)
      Recall (Sensitivity): 0.5366 (53.66%)
      Specificity: 0.6585 (65.85%)
      F1-Score: 0.6414

📈 Train vs Validation Comparison:
   Accuracy Gap: 2.09%
   Precision Gap: 3.59%
   Recall Gap: 1.58%
📊 ROC 曲线已保存到: results/roc_curves.png

============================================================
🚀 EPOCH 4/30 开始训练
   开始时间: 21:20:10
============================================================
🔍 批次 0 数据形状调试:
   原始样本类型: <class 'dict'>
   样本ECG形状: torch.Size([8, 16000])
   样本PCG形状: torch.Size([8, 128, 126])
   提取后ECG形状: torch.Size([8, 1, 16000])
   提取后PCG形状: torch.Size([8, 1, 128, 126])
   提取后标签形状: torch.Size([8])
🔍 开始验证epoch 4...
验证集批次数量: 36
验证数据设备检查:
  ECG设备: cuda:0, 形状: torch.Size([8, 1, 16000])
  PCG设备: cuda:0, 形状: torch.Size([8, 1, 128, 126])
  标签设备: cuda:0, 形状: torch.Size([8])
📊 AUC Score: 0.6696
验证完成: 处理了 287 个样本
🔬 固定学习率: 0.000002 (微调版本)

📊 EPOCH 4 结果:
   🚂 训练集:
      损失: 0.7426
      准确率: 0.5662 (56.62%)
      精确率: 0.6451
      召回率: 0.5662
      F1分数: 0.5887
   🔍 验证集:
      损失: 0.6559
      准确率: 0.6167 (61.67%)
      精确率: 0.7022
      召回率: 0.6167
      F1分数: 0.6352
   ⚙️ 其他:
      学习率: 0.000002
      耗时: 10.4秒
   🎉 Macro F1-Score提升: 0.5960 (新最佳)
   🎉 新的最佳验证准确率: 0.6167 (61.67%)

🔍 Confusion Matrix Analysis:
   📊 Training Confusion Matrix Metrics:
      True Positives (TP): 479
      True Negatives (TN): 171
      False Positives (FP): 146
      False Negatives (FN): 352
      Accuracy: 0.5662 (56.62%)
      Precision: 0.7664 (76.64%)
      Recall (Sensitivity): 0.5764 (57.64%)
      Specificity: 0.5394 (53.94%)
      F1-Score: 0.6580
   📊 Validation Confusion Matrix Metrics:
      True Positives (TP): 121
      True Negatives (TN): 56
      False Positives (FP): 26
      False Negatives (FN): 84
      Accuracy: 0.6167 (61.67%)
      Precision: 0.8231 (82.31%)
      Recall (Sensitivity): 0.5902 (59.02%)
      Specificity: 0.6829 (68.29%)
      F1-Score: 0.6875

📈 Train vs Validation Comparison:
   Accuracy Gap: 5.05%
   Precision Gap: 5.67%
   Recall Gap: 1.38%
📊 ROC 曲线已保存到: results/roc_curves.png

============================================================
🚀 EPOCH 5/30 开始训练
   开始时间: 21:20:27
============================================================
🔍 批次 0 数据形状调试:
   原始样本类型: <class 'dict'>
   样本ECG形状: torch.Size([8, 16000])
   样本PCG形状: torch.Size([8, 128, 126])
   提取后ECG形状: torch.Size([8, 1, 16000])
   提取后PCG形状: torch.Size([8, 1, 128, 126])
   提取后标签形状: torch.Size([8])
🔍 开始验证epoch 5...
验证集批次数量: 36
验证数据设备检查:
  ECG设备: cuda:0, 形状: torch.Size([8, 1, 16000])
  PCG设备: cuda:0, 形状: torch.Size([8, 1, 128, 126])
  标签设备: cuda:0, 形状: torch.Size([8])
📊 AUC Score: 0.6976
验证完成: 处理了 287 个样本
🔬 固定学习率: 0.000002 (微调版本)

📊 EPOCH 5 结果:
   🚂 训练集:
      损失: 0.6745
      准确率: 0.6010 (60.10%)
      精确率: 0.6635
      召回率: 0.6010
      F1分数: 0.6204
   🔍 验证集:
      损失: 0.6323
      准确率: 0.6202 (62.02%)
      精确率: 0.7075
      召回率: 0.6202
      F1分数: 0.6385
   ⚙️ 其他:
      学习率: 0.000002
      耗时: 14.6秒
   🎉 Macro F1-Score提升: 0.6003 (新最佳)
   🎉 新的最佳验证准确率: 0.6202 (62.02%)

🔍 Confusion Matrix Analysis:
   📊 Training Confusion Matrix Metrics:
      True Positives (TP): 518
      True Negatives (TN): 172
      False Positives (FP): 145
      False Negatives (FN): 313
      Accuracy: 0.6010 (60.10%)
      Precision: 0.7813 (78.13%)
      Recall (Sensitivity): 0.6233 (62.33%)
      Specificity: 0.5426 (54.26%)
      F1-Score: 0.6934
   📊 Validation Confusion Matrix Metrics:
      True Positives (TP): 121
      True Negatives (TN): 57
      False Positives (FP): 25
      False Negatives (FN): 84
      Accuracy: 0.6202 (62.02%)
      Precision: 0.8288 (82.88%)
      Recall (Sensitivity): 0.5902 (59.02%)
      Specificity: 0.6951 (69.51%)
      F1-Score: 0.6895

📈 Train vs Validation Comparison:
   Accuracy Gap: 1.92%
   Precision Gap: 4.75%
   Recall Gap: 3.31%
📊 ROC 曲线已保存到: results/roc_curves.png

============================================================
🚀 EPOCH 6/30 开始训练
   开始时间: 21:20:48
============================================================
🔍 批次 0 数据形状调试:
   原始样本类型: <class 'dict'>
   样本ECG形状: torch.Size([8, 16000])
   样本PCG形状: torch.Size([8, 128, 126])
   提取后ECG形状: torch.Size([8, 1, 16000])
   提取后PCG形状: torch.Size([8, 1, 128, 126])
   提取后标签形状: torch.Size([8])
🔍 开始验证epoch 6...
验证集批次数量: 36
验证数据设备检查:
  ECG设备: cuda:0, 形状: torch.Size([8, 1, 16000])
  PCG设备: cuda:0, 形状: torch.Size([8, 1, 128, 126])
  标签设备: cuda:0, 形状: torch.Size([8])
📊 AUC Score: 0.7174
验证完成: 处理了 287 个样本
🔬 固定学习率: 0.000002 (微调版本)

📊 EPOCH 6 结果:
   🚂 训练集:
      损失: 0.6740
      准确率: 0.6089 (60.89%)
      精确率: 0.6737
      召回率: 0.6089
      F1分数: 0.6282
   🔍 验证集:
      损失: 0.6745
      准确率: 0.5575 (55.75%)
      精确率: 0.7565
      召回率: 0.5575
      F1分数: 0.5657
   ⚙️ 其他:
      学习率: 0.000002
      耗时: 12.9秒
   ⏰ 无改善 1/20 (当前: 0.5564, 最佳: 0.6003)

🔍 Confusion Matrix Analysis:
   📊 Training Confusion Matrix Metrics:
      True Positives (TP): 519
      True Negatives (TN): 180
      False Positives (FP): 137
      False Negatives (FN): 312
      Accuracy: 0.6089 (60.89%)
      Precision: 0.7912 (79.12%)
      Recall (Sensitivity): 0.6245 (62.45%)
      Specificity: 0.5678 (56.78%)
      F1-Score: 0.6980
   📊 Validation Confusion Matrix Metrics:
      True Positives (TP): 87
      True Negatives (TN): 73
      False Positives (FP): 9
      False Negatives (FN): 118
      Accuracy: 0.5575 (55.75%)
      Precision: 0.9062 (90.62%)
      Recall (Sensitivity): 0.4244 (42.44%)
      Specificity: 0.8902 (89.02%)
      F1-Score: 0.5781

📈 Train vs Validation Comparison:
   Accuracy Gap: 5.14%
   Precision Gap: 11.51%
   Recall Gap: 20.02%
📊 ROC 曲线已保存到: results/roc_curves.png

============================================================
🚀 EPOCH 7/30 开始训练
   开始时间: 21:21:06
============================================================
🔍 批次 0 数据形状调试:
   原始样本类型: <class 'dict'>
   样本ECG形状: torch.Size([8, 16000])
   样本PCG形状: torch.Size([8, 128, 126])
   提取后ECG形状: torch.Size([8, 1, 16000])
   提取后PCG形状: torch.Size([8, 1, 128, 126])
   提取后标签形状: torch.Size([8])
🔍 开始验证epoch 7...
验证集批次数量: 36
验证数据设备检查:
  ECG设备: cuda:0, 形状: torch.Size([8, 1, 16000])
  PCG设备: cuda:0, 形状: torch.Size([8, 1, 128, 126])
  标签设备: cuda:0, 形状: torch.Size([8])
📊 AUC Score: 0.7351
验证完成: 处理了 287 个样本
🔬 固定学习率: 0.000002 (微调版本)

📊 EPOCH 7 结果:
   🚂 训练集:
      损失: 0.6726
      准确率: 0.6315 (63.15%)
      精确率: 0.6938
      召回率: 0.6315
      F1分数: 0.6495
   🔍 验证集:
      损失: 0.6407
      准确率: 0.5854 (58.54%)
      精确率: 0.7549
      召回率: 0.5854
      F1分数: 0.5982
   ⚙️ 其他:
      学习率: 0.000002
      耗时: 11.2秒
   ⏰ 无改善 2/20 (当前: 0.5819, 最佳: 0.6003)

🔍 Confusion Matrix Analysis:
   📊 Training Confusion Matrix Metrics:
      True Positives (TP): 534
      True Negatives (TN): 191
      False Positives (FP): 126
      False Negatives (FN): 297
      Accuracy: 0.6315 (63.15%)
      Precision: 0.8091 (80.91%)
      Recall (Sensitivity): 0.6426 (64.26%)
      Specificity: 0.6025 (60.25%)
      F1-Score: 0.7163
   📊 Validation Confusion Matrix Metrics:
      True Positives (TP): 97
      True Negatives (TN): 71
      False Positives (FP): 11
      False Negatives (FN): 108
      Accuracy: 0.5854 (58.54%)
      Precision: 0.8981 (89.81%)
      Recall (Sensitivity): 0.4732 (47.32%)
      Specificity: 0.8659 (86.59%)
      F1-Score: 0.6198

📈 Train vs Validation Comparison:
   Accuracy Gap: 4.62%
   Precision Gap: 8.91%
   Recall Gap: 16.94%
📊 ROC 曲线已保存到: results/roc_curves.png

============================================================
🚀 EPOCH 8/30 开始训练
   开始时间: 21:21:23
============================================================
🔍 批次 0 数据形状调试:
   原始样本类型: <class 'dict'>
   样本ECG形状: torch.Size([8, 16000])
   样本PCG形状: torch.Size([8, 128, 126])
   提取后ECG形状: torch.Size([8, 1, 16000])
   提取后PCG形状: torch.Size([8, 1, 128, 126])
   提取后标签形状: torch.Size([8])
🔍 开始验证epoch 8...
验证集批次数量: 36
验证数据设备检查:
  ECG设备: cuda:0, 形状: torch.Size([8, 1, 16000])
  PCG设备: cuda:0, 形状: torch.Size([8, 1, 128, 126])
  标签设备: cuda:0, 形状: torch.Size([8])
📊 AUC Score: 0.7442
验证完成: 处理了 287 个样本
🔬 固定学习率: 0.000002 (微调版本)

📊 EPOCH 8 结果:
   🚂 训练集:
      损失: 0.6456
      准确率: 0.6672 (66.72%)
      精确率: 0.7088
      召回率: 0.6672
      F1分数: 0.6809
   🔍 验证集:
      损失: 0.6292
      准确率: 0.6307 (63.07%)
      精确率: 0.7666
      召回率: 0.6307
      F1分数: 0.6461
   ⚙️ 其他:
      学习率: 0.000002
      耗时: 13.0秒
   🎉 Macro F1-Score提升: 0.6230 (新最佳)
   🎉 新的最佳验证准确率: 0.6307 (63.07%)

🔍 Confusion Matrix Analysis:
   📊 Training Confusion Matrix Metrics:
      True Positives (TP): 579
      True Negatives (TN): 187
      False Positives (FP): 130
      False Negatives (FN): 252
      Accuracy: 0.6672 (66.72%)
      Precision: 0.8166 (81.66%)
      Recall (Sensitivity): 0.6968 (69.68%)
      Specificity: 0.5899 (58.99%)
      F1-Score: 0.7519
   📊 Validation Confusion Matrix Metrics:
      True Positives (TP): 111
      True Negatives (TN): 70
      False Positives (FP): 12
      False Negatives (FN): 94
      Accuracy: 0.6307 (63.07%)
      Precision: 0.9024 (90.24%)
      Recall (Sensitivity): 0.5415 (54.15%)
      Specificity: 0.8537 (85.37%)
      F1-Score: 0.6768

📈 Train vs Validation Comparison:
   Accuracy Gap: 3.66%
   Precision Gap: 8.58%
   Recall Gap: 15.53%
📊 ROC 曲线已保存到: results/roc_curves.png

============================================================
🚀 EPOCH 9/30 开始训练
   开始时间: 21:21:42
============================================================
🔍 批次 0 数据形状调试:
   原始样本类型: <class 'dict'>
   样本ECG形状: torch.Size([8, 16000])
   样本PCG形状: torch.Size([8, 128, 126])
   提取后ECG形状: torch.Size([8, 1, 16000])
   提取后PCG形状: torch.Size([8, 1, 128, 126])
   提取后标签形状: torch.Size([8])
🔍 开始验证epoch 9...
验证集批次数量: 36
验证数据设备检查:
  ECG设备: cuda:0, 形状: torch.Size([8, 1, 16000])
  PCG设备: cuda:0, 形状: torch.Size([8, 1, 128, 126])
  标签设备: cuda:0, 形状: torch.Size([8])
📊 AUC Score: 0.7667
验证完成: 处理了 287 个样本
🔬 固定学习率: 0.000002 (微调版本)

📊 EPOCH 9 结果:
   🚂 训练集:
      损失: 0.6209
      准确率: 0.6672 (66.72%)
      精确率: 0.7179
      召回率: 0.6672
      F1分数: 0.6824
   🔍 验证集:
      损失: 0.5646
      准确率: 0.6934 (69.34%)
      精确率: 0.7340
      召回率: 0.6934
      F1分数: 0.7054
   ⚙️ 其他:
      学习率: 0.000002
      耗时: 13.7秒
   🎉 Macro F1-Score提升: 0.6591 (新最佳)
   🎉 新的最佳验证准确率: 0.6934 (69.34%)

🔍 Confusion Matrix Analysis:
   📊 Training Confusion Matrix Metrics:
      True Positives (TP): 567
      True Negatives (TN): 199
      False Positives (FP): 118
      False Negatives (FN): 264
      Accuracy: 0.6672 (66.72%)
      Precision: 0.8277 (82.77%)
      Recall (Sensitivity): 0.6823 (68.23%)
      Specificity: 0.6278 (62.78%)
      F1-Score: 0.7480
   📊 Validation Confusion Matrix Metrics:
      True Positives (TP): 145
      True Negatives (TN): 54
      False Positives (FP): 28
      False Negatives (FN): 60
      Accuracy: 0.6934 (69.34%)
      Precision: 0.8382 (83.82%)
      Recall (Sensitivity): 0.7073 (70.73%)
      Specificity: 0.6585 (65.85%)
      F1-Score: 0.7672

📈 Train vs Validation Comparison:
   Accuracy Gap: 2.61%
   Precision Gap: 1.04%
   Recall Gap: 2.50%
📊 ROC 曲线已保存到: results/roc_curves.png

============================================================
🚀 EPOCH 10/30 开始训练
   开始时间: 21:22:02
============================================================
🔍 批次 0 数据形状调试:
   原始样本类型: <class 'dict'>
   样本ECG形状: torch.Size([8, 16000])
   样本PCG形状: torch.Size([8, 128, 126])
   提取后ECG形状: torch.Size([8, 1, 16000])
   提取后PCG形状: torch.Size([8, 1, 128, 126])
   提取后标签形状: torch.Size([8])
🔍 开始验证epoch 10...
验证集批次数量: 36
验证数据设备检查:
  ECG设备: cuda:0, 形状: torch.Size([8, 1, 16000])
  PCG设备: cuda:0, 形状: torch.Size([8, 1, 128, 126])
  标签设备: cuda:0, 形状: torch.Size([8])
📊 AUC Score: 0.7540
验证完成: 处理了 287 个样本
🔬 固定学习率: 0.000002 (微调版本)

📊 EPOCH 10 结果:
   🚂 训练集:
      损失: 0.6199
      准确率: 0.6481 (64.81%)
      精确率: 0.6959
      召回率: 0.6481
      F1分数: 0.6635
   🔍 验证集:
      损失: 0.5856
      准确率: 0.6655 (66.55%)
      精确率: 0.7544
      召回率: 0.6655
      F1分数: 0.6816
   ⚙️ 其他:
      学习率: 0.000002
      耗时: 11.3秒
   ⏰ 无改善 1/20 (当前: 0.6486, 最佳: 0.6591)

🔍 Confusion Matrix Analysis:
   📊 Training Confusion Matrix Metrics:
      True Positives (TP): 561
      True Negatives (TN): 183
      False Positives (FP): 134
      False Negatives (FN): 270
      Accuracy: 0.6481 (64.81%)
      Precision: 0.8072 (80.72%)
      Recall (Sensitivity): 0.6751 (67.51%)
      Specificity: 0.5773 (57.73%)
      F1-Score: 0.7353
   📊 Validation Confusion Matrix Metrics:
      True Positives (TP): 127
      True Negatives (TN): 64
      False Positives (FP): 18
      False Negatives (FN): 78
      Accuracy: 0.6655 (66.55%)
      Precision: 0.8759 (87.59%)
      Recall (Sensitivity): 0.6195 (61.95%)
      Specificity: 0.7805 (78.05%)
      F1-Score: 0.7257

📈 Train vs Validation Comparison:
   Accuracy Gap: 1.74%
   Precision Gap: 6.87%
   Recall Gap: 5.56%
📊 ROC 曲线已保存到: results/roc_curves.png

============================================================
🚀 EPOCH 11/30 开始训练
   开始时间: 21:22:18
============================================================
🔍 批次 0 数据形状调试:
   原始样本类型: <class 'dict'>
   样本ECG形状: torch.Size([8, 16000])
   样本PCG形状: torch.Size([8, 128, 126])
   提取后ECG形状: torch.Size([8, 1, 16000])
   提取后PCG形状: torch.Size([8, 1, 128, 126])
   提取后标签形状: torch.Size([8])
🔍 开始验证epoch 11...
验证集批次数量: 36
验证数据设备检查:
  ECG设备: cuda:0, 形状: torch.Size([8, 1, 16000])
  PCG设备: cuda:0, 形状: torch.Size([8, 1, 128, 126])
  标签设备: cuda:0, 形状: torch.Size([8])
📊 AUC Score: 0.7626
验证完成: 处理了 287 个样本
🔬 固定学习率: 0.000002 (微调版本)

📊 EPOCH 11 结果:
   🚂 训练集:
      损失: 0.5760
      准确率: 0.6934 (69.34%)
      精确率: 0.7335
      召回率: 0.6934
      F1分数: 0.7060
   🔍 验证集:
      损失: 0.8022
      准确率: 0.4460 (44.60%)
      精确率: 0.7962
      召回率: 0.4460
      F1分数: 0.4096
   ⚙️ 其他:
      学习率: 0.000002
      耗时: 12.1秒
   ⏰ 无改善 2/20 (当前: 0.4381, 最佳: 0.6591)

🔍 Confusion Matrix Analysis:
   📊 Training Confusion Matrix Metrics:
      True Positives (TP): 594
      True Negatives (TN): 202
      False Positives (FP): 115
      False Negatives (FN): 237
      Accuracy: 0.6934 (69.34%)
      Precision: 0.8378 (83.78%)
      Recall (Sensitivity): 0.7148 (71.48%)
      Specificity: 0.6372 (63.72%)
      F1-Score: 0.7714
   📊 Validation Confusion Matrix Metrics:
      True Positives (TP): 47
      True Negatives (TN): 81
      False Positives (FP): 1
      False Negatives (FN): 158
      Accuracy: 0.4460 (44.60%)
      Precision: 0.9792 (97.92%)
      Recall (Sensitivity): 0.2293 (22.93%)
      Specificity: 0.9878 (98.78%)
      F1-Score: 0.3715

📈 Train vs Validation Comparison:
   Accuracy Gap: 24.74%
   Precision Gap: 14.14%
   Recall Gap: 48.55%
📊 ROC 曲线已保存到: results/roc_curves.png

============================================================
🚀 EPOCH 12/30 开始训练
   开始时间: 21:22:36
============================================================
🔍 批次 0 数据形状调试:
   原始样本类型: <class 'dict'>
   样本ECG形状: torch.Size([8, 16000])
   样本PCG形状: torch.Size([8, 128, 126])
   提取后ECG形状: torch.Size([8, 1, 16000])
   提取后PCG形状: torch.Size([8, 1, 128, 126])
   提取后标签形状: torch.Size([8])
🔍 开始验证epoch 12...
验证集批次数量: 36
验证数据设备检查:
  ECG设备: cuda:0, 形状: torch.Size([8, 1, 16000])
  PCG设备: cuda:0, 形状: torch.Size([8, 1, 128, 126])
  标签设备: cuda:0, 形状: torch.Size([8])
📊 AUC Score: 0.7816
验证完成: 处理了 287 个样本
🔬 固定学习率: 0.000002 (微调版本)

📊 EPOCH 12 结果:
   🚂 训练集:
      损失: 0.5742
      准确率: 0.7003 (70.03%)
      精确率: 0.7409
      召回率: 0.7003
      F1分数: 0.7128
   🔍 验证集:
      损失: 0.5571
      准确率: 0.7038 (70.38%)
      精确率: 0.7341
      召回率: 0.7038
      F1分数: 0.7138
   ⚙️ 其他:
      学习率: 0.000002
      耗时: 12.5秒
   🎉 Macro F1-Score提升: 0.6647 (新最佳)
   🎉 新的最佳验证准确率: 0.7038 (70.38%)

🔍 Confusion Matrix Analysis:
   📊 Training Confusion Matrix Metrics:
      True Positives (TP): 597
      True Negatives (TN): 207
      False Positives (FP): 110
      False Negatives (FN): 234
      Accuracy: 0.7003 (70.03%)
      Precision: 0.8444 (84.44%)
      Recall (Sensitivity): 0.7184 (71.84%)
      Specificity: 0.6530 (65.30%)
      F1-Score: 0.7763
   📊 Validation Confusion Matrix Metrics:
      True Positives (TP): 150
      True Negatives (TN): 52
      False Positives (FP): 30
      False Negatives (FN): 55
      Accuracy: 0.7038 (70.38%)
      Precision: 0.8333 (83.33%)
      Recall (Sensitivity): 0.7317 (73.17%)
      Specificity: 0.6341 (63.41%)
      F1-Score: 0.7792

📈 Train vs Validation Comparison:
   Accuracy Gap: 0.35%
   Precision Gap: 1.11%
   Recall Gap: 1.33%
📊 ROC 曲线已保存到: results/roc_curves.png

============================================================
🚀 EPOCH 13/30 开始训练
   开始时间: 21:22:55
============================================================
🔍 批次 0 数据形状调试:
   原始样本类型: <class 'dict'>
   样本ECG形状: torch.Size([8, 16000])
   样本PCG形状: torch.Size([8, 128, 126])
   提取后ECG形状: torch.Size([8, 1, 16000])
   提取后PCG形状: torch.Size([8, 1, 128, 126])
   提取后标签形状: torch.Size([8])
🔍 开始验证epoch 13...
验证集批次数量: 36
验证数据设备检查:
  ECG设备: cuda:0, 形状: torch.Size([8, 1, 16000])
  PCG设备: cuda:0, 形状: torch.Size([8, 1, 128, 126])
  标签设备: cuda:0, 形状: torch.Size([8])
📊 AUC Score: 0.7872
验证完成: 处理了 287 个样本
🔬 固定学习率: 0.000002 (微调版本)

📊 EPOCH 13 结果:
   🚂 训练集:
      损失: 0.5244
      准确率: 0.7169 (71.69%)
      精确率: 0.7562
      召回率: 0.7169
      F1分数: 0.7286
   🔍 验证集:
      损失: 0.5632
      准确率: 0.6690 (66.90%)
      精确率: 0.7447
      召回率: 0.6690
      F1分数: 0.6848
   ⚙️ 其他:
      学习率: 0.000002
      耗时: 11.9秒
   ⏰ 无改善 1/20 (当前: 0.6481, 最佳: 0.6647)

🔍 Confusion Matrix Analysis:
   📊 Training Confusion Matrix Metrics:
      True Positives (TP): 607
      True Negatives (TN): 216
      False Positives (FP): 101
      False Negatives (FN): 224
      Accuracy: 0.7169 (71.69%)
      Precision: 0.8573 (85.73%)
      Recall (Sensitivity): 0.7304 (73.04%)
      Specificity: 0.6814 (68.14%)
      F1-Score: 0.7888
   📊 Validation Confusion Matrix Metrics:
      True Positives (TP): 131
      True Negatives (TN): 61
      False Positives (FP): 21
      False Negatives (FN): 74
      Accuracy: 0.6690 (66.90%)
      Precision: 0.8618 (86.18%)
      Recall (Sensitivity): 0.6390 (63.90%)
      Specificity: 0.7439 (74.39%)
      F1-Score: 0.7339

📈 Train vs Validation Comparison:
   Accuracy Gap: 4.79%
   Precision Gap: 0.45%
   Recall Gap: 9.14%
📊 ROC 曲线已保存到: results/roc_curves.png

============================================================
🚀 EPOCH 14/30 开始训练
   开始时间: 21:23:12
============================================================
🔍 批次 0 数据形状调试:
   原始样本类型: <class 'dict'>
   样本ECG形状: torch.Size([8, 16000])
   样本PCG形状: torch.Size([8, 128, 126])
   提取后ECG形状: torch.Size([8, 1, 16000])
   提取后PCG形状: torch.Size([8, 1, 128, 126])
   提取后标签形状: torch.Size([8])
🔍 开始验证epoch 14...
验证集批次数量: 36
验证数据设备检查:
  ECG设备: cuda:0, 形状: torch.Size([8, 1, 16000])
  PCG设备: cuda:0, 形状: torch.Size([8, 1, 128, 126])
  标签设备: cuda:0, 形状: torch.Size([8])
📊 AUC Score: 0.8139
验证完成: 处理了 287 个样本
🔬 固定学习率: 0.000002 (微调版本)

📊 EPOCH 14 结果:
   🚂 训练集:
      损失: 0.5116
      准确率: 0.7186 (71.86%)
      精确率: 0.7534
      召回率: 0.7186
      F1分数: 0.7295
   🔍 验证集:
      损失: 0.9790
      准确率: 0.4042 (40.42%)
      精确率: 0.8069
      召回率: 0.4042
      F1分数: 0.3431
   ⚙️ 其他:
      学习率: 0.000002
      耗时: 12.3秒
   ⏰ 无改善 2/20 (当前: 0.3870, 最佳: 0.6647)

🔍 Confusion Matrix Analysis:
   📊 Training Confusion Matrix Metrics:
      True Positives (TP): 614
      True Negatives (TN): 211
      False Positives (FP): 106
      False Negatives (FN): 217
      Accuracy: 0.7186 (71.86%)
      Precision: 0.8528 (85.28%)
      Recall (Sensitivity): 0.7389 (73.89%)
      Specificity: 0.6656 (66.56%)
      F1-Score: 0.7917
   📊 Validation Confusion Matrix Metrics:
      True Positives (TP): 34
      True Negatives (TN): 82
      False Positives (FP): 0
      False Negatives (FN): 171
      Accuracy: 0.4042 (40.42%)
      Precision: 1.0000 (100.00%)
      Recall (Sensitivity): 0.1659 (16.59%)
      Specificity: 1.0000 (100.00%)
      F1-Score: 0.2845

📈 Train vs Validation Comparison:
   Accuracy Gap: 31.45%
   Precision Gap: 14.72%
   Recall Gap: 57.30%
📊 ROC 曲线已保存到: results/roc_curves.png

============================================================
🚀 EPOCH 15/30 开始训练
   开始时间: 21:23:29
============================================================
🔍 批次 0 数据形状调试:
   原始样本类型: <class 'dict'>
   样本ECG形状: torch.Size([8, 16000])
   样本PCG形状: torch.Size([8, 128, 126])
   提取后ECG形状: torch.Size([8, 1, 16000])
   提取后PCG形状: torch.Size([8, 1, 128, 126])
   提取后标签形状: torch.Size([8])
🔍 开始验证epoch 15...
验证集批次数量: 36
验证数据设备检查:
  ECG设备: cuda:0, 形状: torch.Size([8, 1, 16000])
  PCG设备: cuda:0, 形状: torch.Size([8, 1, 128, 126])
  标签设备: cuda:0, 形状: torch.Size([8])
📊 AUC Score: 0.8228
验证完成: 处理了 287 个样本
🔬 固定学习率: 0.000002 (微调版本)

📊 EPOCH 15 结果:
   🚂 训练集:
      损失: 0.4665
      准确率: 0.7587 (75.87%)
      精确率: 0.7865
      召回率: 0.7587
      F1分数: 0.7672
   🔍 验证集:
      损失: 0.7894
      准确率: 0.4948 (49.48%)
      精确率: 0.8056
      召回率: 0.4948
      F1分数: 0.4771
   ⚙️ 其他:
      学习率: 0.000002
      耗时: 11.5秒
   ⏰ 无改善 3/20 (当前: 0.4923, 最佳: 0.6647)

🔍 Confusion Matrix Analysis:
   📊 Training Confusion Matrix Metrics:
      True Positives (TP): 644
      True Negatives (TN): 227
      False Positives (FP): 90
      False Negatives (FN): 187
      Accuracy: 0.7587 (75.87%)
      Precision: 0.8774 (87.74%)
      Recall (Sensitivity): 0.7750 (77.50%)
      Specificity: 0.7161 (71.61%)
      F1-Score: 0.8230
   📊 Validation Confusion Matrix Metrics:
      True Positives (TP): 61
      True Negatives (TN): 81
      False Positives (FP): 1
      False Negatives (FN): 144
      Accuracy: 0.4948 (49.48%)
      Precision: 0.9839 (98.39%)
      Recall (Sensitivity): 0.2976 (29.76%)
      Specificity: 0.9878 (98.78%)
      F1-Score: 0.4569

📈 Train vs Validation Comparison:
   Accuracy Gap: 26.39%
   Precision Gap: 10.65%
   Recall Gap: 47.74%
📊 ROC 曲线已保存到: results/roc_curves.png

============================================================
🚀 EPOCH 16/30 开始训练
   开始时间: 21:23:46
============================================================
🔍 批次 0 数据形状调试:
   原始样本类型: <class 'dict'>
   样本ECG形状: torch.Size([8, 16000])
   样本PCG形状: torch.Size([8, 128, 126])
   提取后ECG形状: torch.Size([8, 1, 16000])
   提取后PCG形状: torch.Size([8, 1, 128, 126])
   提取后标签形状: torch.Size([8])
🔍 开始验证epoch 16...
验证集批次数量: 36
验证数据设备检查:
  ECG设备: cuda:0, 形状: torch.Size([8, 1, 16000])
  PCG设备: cuda:0, 形状: torch.Size([8, 1, 128, 126])
  标签设备: cuda:0, 形状: torch.Size([8])
📊 AUC Score: 0.8219
验证完成: 处理了 287 个样本
🔬 固定学习率: 0.000002 (微调版本)

📊 EPOCH 16 结果:
   🚂 训练集:
      损失: 0.5093
      准确率: 0.7265 (72.65%)
      精确率: 0.7598
      召回率: 0.7265
      F1分数: 0.7369
   🔍 验证集:
      损失: 0.6500
      准确率: 0.5749 (57.49%)
      精确率: 0.7893
      召回率: 0.5749
      F1分数: 0.5813
   ⚙️ 其他:
      学习率: 0.000002
      耗时: 11.6秒
   ⏰ 无改善 4/20 (当前: 0.5743, 最佳: 0.6647)

🔍 Confusion Matrix Analysis:
   📊 Training Confusion Matrix Metrics:
      True Positives (TP): 620
      True Negatives (TN): 214
      False Positives (FP): 103
      False Negatives (FN): 211
      Accuracy: 0.7265 (72.65%)
      Precision: 0.8575 (85.75%)
      Recall (Sensitivity): 0.7461 (74.61%)
      Specificity: 0.6751 (67.51%)
      F1-Score: 0.7979
   📊 Validation Confusion Matrix Metrics:
      True Positives (TP): 88
      True Negatives (TN): 77
      False Positives (FP): 5
      False Negatives (FN): 117
      Accuracy: 0.5749 (57.49%)
      Precision: 0.9462 (94.62%)
      Recall (Sensitivity): 0.4293 (42.93%)
      Specificity: 0.9390 (93.90%)
      F1-Score: 0.5906

📈 Train vs Validation Comparison:
   Accuracy Gap: 15.16%
   Precision Gap: 8.87%
   Recall Gap: 31.68%
📊 ROC 曲线已保存到: results/roc_curves.png

============================================================
🚀 EPOCH 17/30 开始训练
   开始时间: 21:24:03
============================================================
🔍 批次 0 数据形状调试:
   原始样本类型: <class 'dict'>
   样本ECG形状: torch.Size([8, 16000])
   样本PCG形状: torch.Size([8, 128, 126])
   提取后ECG形状: torch.Size([8, 1, 16000])
   提取后PCG形状: torch.Size([8, 1, 128, 126])
   提取后标签形状: torch.Size([8])
🔍 开始验证epoch 17...
验证集批次数量: 36
验证数据设备检查:
  ECG设备: cuda:0, 形状: torch.Size([8, 1, 16000])
  PCG设备: cuda:0, 形状: torch.Size([8, 1, 128, 126])
  标签设备: cuda:0, 形状: torch.Size([8])
📊 AUC Score: 0.8241
验证完成: 处理了 287 个样本
🔬 固定学习率: 0.000002 (微调版本)

📊 EPOCH 17 结果:
   🚂 训练集:
      损失: 0.4490
      准确率: 0.7500 (75.00%)
      精确率: 0.7824
      召回率: 0.7500
      F1分数: 0.7596
   🔍 验证集:
      损失: 0.7481
      准确率: 0.5540 (55.40%)
      精确率: 0.8165
      召回率: 0.5540
      F1分数: 0.5520
   ⚙️ 其他:
      学习率: 0.000002
      耗时: 12.3秒
   ⏰ 无改善 5/20 (当前: 0.5540, 最佳: 0.6647)

🔍 Confusion Matrix Analysis:
   📊 Training Confusion Matrix Metrics:
      True Positives (TP): 633
      True Negatives (TN): 228
      False Positives (FP): 89
      False Negatives (FN): 198
      Accuracy: 0.7500 (75.00%)
      Precision: 0.8767 (87.67%)
      Recall (Sensitivity): 0.7617 (76.17%)
      Specificity: 0.7192 (71.92%)
      F1-Score: 0.8152
   📊 Validation Confusion Matrix Metrics:
      True Positives (TP): 78
      True Negatives (TN): 81
      False Positives (FP): 1
      False Negatives (FN): 127
      Accuracy: 0.5540 (55.40%)
      Precision: 0.9873 (98.73%)
      Recall (Sensitivity): 0.3805 (38.05%)
      Specificity: 0.9878 (98.78%)
      F1-Score: 0.5493

📈 Train vs Validation Comparison:
   Accuracy Gap: 19.60%
   Precision Gap: 11.06%
   Recall Gap: 38.12%
📊 ROC 曲线已保存到: results/roc_curves.png

============================================================
🚀 EPOCH 18/30 开始训练
   开始时间: 21:24:20
============================================================
🔍 批次 0 数据形状调试:
   原始样本类型: <class 'dict'>
   样本ECG形状: torch.Size([8, 16000])
   样本PCG形状: torch.Size([8, 128, 126])
   提取后ECG形状: torch.Size([8, 1, 16000])
   提取后PCG形状: torch.Size([8, 1, 128, 126])
   提取后标签形状: torch.Size([8])
🔍 开始验证epoch 18...
验证集批次数量: 36
验证数据设备检查:
  ECG设备: cuda:0, 形状: torch.Size([8, 1, 16000])
  PCG设备: cuda:0, 形状: torch.Size([8, 1, 128, 126])
  标签设备: cuda:0, 形状: torch.Size([8])
📊 AUC Score: 0.8280
验证完成: 处理了 287 个样本
🔬 固定学习率: 0.000002 (微调版本)

📊 EPOCH 18 结果:
   🚂 训练集:
      损失: 0.4354
      准确率: 0.7631 (76.31%)
      精确率: 0.7857
      召回率: 0.7631
      F1分数: 0.7705
   🔍 验证集:
      损失: 0.6103
      准确率: 0.7875 (78.75%)
      精确率: 0.7762
      召回率: 0.7875
      F1分数: 0.7731
   ⚙️ 其他:
      学习率: 0.000002
      耗时: 13.0秒
   🎉 Macro F1-Score提升: 0.7076 (新最佳)
   🎉 新的最佳验证准确率: 0.7875 (78.75%)

🔍 Confusion Matrix Analysis:
   📊 Training Confusion Matrix Metrics:
      True Positives (TP): 654
      True Negatives (TN): 222
      False Positives (FP): 95
      False Negatives (FN): 177
      Accuracy: 0.7631 (76.31%)
      Precision: 0.8732 (87.32%)
      Recall (Sensitivity): 0.7870 (78.70%)
      Specificity: 0.7003 (70.03%)
      F1-Score: 0.8278
   📊 Validation Confusion Matrix Metrics:
      True Positives (TP): 188
      True Negatives (TN): 38
      False Positives (FP): 44
      False Negatives (FN): 17
      Accuracy: 0.7875 (78.75%)
      Precision: 0.8103 (81.03%)
      Recall (Sensitivity): 0.9171 (91.71%)
      Specificity: 0.4634 (46.34%)
      F1-Score: 0.8604

📈 Train vs Validation Comparison:
   Accuracy Gap: 2.44%
   Precision Gap: 6.28%
   Recall Gap: 13.01%
📊 ROC 曲线已保存到: results/roc_curves.png

============================================================
🚀 EPOCH 19/30 开始训练
   开始时间: 21:24:40
============================================================
🔍 批次 0 数据形状调试:
   原始样本类型: <class 'dict'>
   样本ECG形状: torch.Size([8, 16000])
   样本PCG形状: torch.Size([8, 128, 126])
   提取后ECG形状: torch.Size([8, 1, 16000])
   提取后PCG形状: torch.Size([8, 1, 128, 126])
   提取后标签形状: torch.Size([8])
🔍 开始验证epoch 19...
验证集批次数量: 36
验证数据设备检查:
  ECG设备: cuda:0, 形状: torch.Size([8, 1, 16000])
  PCG设备: cuda:0, 形状: torch.Size([8, 1, 128, 126])
  标签设备: cuda:0, 形状: torch.Size([8])
📊 AUC Score: 0.8325
验证完成: 处理了 287 个样本
🔬 固定学习率: 0.000002 (微调版本)

📊 EPOCH 19 结果:
   🚂 训练集:
      损失: 0.4454
      准确率: 0.7753 (77.53%)
      精确率: 0.7999
      召回率: 0.7753
      F1分数: 0.7828
   🔍 验证集:
      损失: 0.5310
      准确率: 0.6899 (68.99%)
      精确率: 0.7935
      召回率: 0.6899
      F1分数: 0.7046
   ⚙️ 其他:
      学习率: 0.000002
      耗时: 11.7秒
   ⏰ 无改善 1/20 (当前: 0.6776, 最佳: 0.7076)

🔍 Confusion Matrix Analysis:
   📊 Training Confusion Matrix Metrics:
      True Positives (TP): 657
      True Negatives (TN): 233
      False Positives (FP): 84
      False Negatives (FN): 174
      Accuracy: 0.7753 (77.53%)
      Precision: 0.8866 (88.66%)
      Recall (Sensitivity): 0.7906 (79.06%)
      Specificity: 0.7350 (73.50%)
      F1-Score: 0.8359
   📊 Validation Confusion Matrix Metrics:
      True Positives (TP): 127
      True Negatives (TN): 71
      False Positives (FP): 11
      False Negatives (FN): 78
      Accuracy: 0.6899 (68.99%)
      Precision: 0.9203 (92.03%)
      Recall (Sensitivity): 0.6195 (61.95%)
      Specificity: 0.8659 (86.59%)
      F1-Score: 0.7405

📈 Train vs Validation Comparison:
   Accuracy Gap: 8.54%
   Precision Gap: 3.37%
   Recall Gap: 17.11%
📊 ROC 曲线已保存到: results/roc_curves.png

============================================================
🚀 EPOCH 20/30 开始训练
   开始时间: 21:24:56
============================================================
🔍 批次 0 数据形状调试:
   原始样本类型: <class 'dict'>
   样本ECG形状: torch.Size([8, 16000])
   样本PCG形状: torch.Size([8, 128, 126])
   提取后ECG形状: torch.Size([8, 1, 16000])
   提取后PCG形状: torch.Size([8, 1, 128, 126])
   提取后标签形状: torch.Size([8])
🔍 开始验证epoch 20...
验证集批次数量: 36
验证数据设备检查:
  ECG设备: cuda:0, 形状: torch.Size([8, 1, 16000])
  PCG设备: cuda:0, 形状: torch.Size([8, 1, 128, 126])
  标签设备: cuda:0, 形状: torch.Size([8])
📊 AUC Score: 0.8285
验证完成: 处理了 287 个样本
🔬 固定学习率: 0.000002 (微调版本)

📊 EPOCH 20 结果:
   🚂 训练集:
      损失: 0.4175
      准确率: 0.7901 (79.01%)
      精确率: 0.8086
      召回率: 0.7901
      F1分数: 0.7961
   🔍 验证集:
      损失: 0.6513
      准确率: 0.5854 (58.54%)
      精确率: 0.7923
      召回率: 0.5854
      F1分数: 0.5931
   ⚙️ 其他:
      学习率: 0.000002
      耗时: 12.9秒
   ⏰ 无改善 2/20 (当前: 0.5844, 最佳: 0.7076)

🔍 Confusion Matrix Analysis:
   📊 Training Confusion Matrix Metrics:
      True Positives (TP): 674
      True Negatives (TN): 233
      False Positives (FP): 84
      False Negatives (FN): 157
      Accuracy: 0.7901 (79.01%)
      Precision: 0.8892 (88.92%)
      Recall (Sensitivity): 0.8111 (81.11%)
      Specificity: 0.7350 (73.50%)
      F1-Score: 0.8483
   📊 Validation Confusion Matrix Metrics:
      True Positives (TP): 91
      True Negatives (TN): 77
      False Positives (FP): 5
      False Negatives (FN): 114
      Accuracy: 0.5854 (58.54%)
      Precision: 0.9479 (94.79%)
      Recall (Sensitivity): 0.4439 (44.39%)
      Specificity: 0.9390 (93.90%)
      F1-Score: 0.6047

📈 Train vs Validation Comparison:
   Accuracy Gap: 20.47%
   Precision Gap: 5.87%
   Recall Gap: 36.72%
📊 ROC 曲线已保存到: results/roc_curves.png

============================================================
🚀 EPOCH 21/30 开始训练
   开始时间: 21:25:15
============================================================
🔍 批次 0 数据形状调试:
   原始样本类型: <class 'dict'>
   样本ECG形状: torch.Size([8, 16000])
   样本PCG形状: torch.Size([8, 128, 126])
   提取后ECG形状: torch.Size([8, 1, 16000])
   提取后PCG形状: torch.Size([8, 1, 128, 126])
   提取后标签形状: torch.Size([8])
🔍 开始验证epoch 21...
验证集批次数量: 36
验证数据设备检查:
  ECG设备: cuda:0, 形状: torch.Size([8, 1, 16000])
  PCG设备: cuda:0, 形状: torch.Size([8, 1, 128, 126])
  标签设备: cuda:0, 形状: torch.Size([8])
📊 AUC Score: 0.8255
验证完成: 处理了 287 个样本
🔬 固定学习率: 0.000002 (微调版本)

📊 EPOCH 21 结果:
   🚂 训练集:
      损失: 0.4400
      准确率: 0.7509 (75.09%)
      精确率: 0.7728
      召回率: 0.7509
      F1分数: 0.7584
   🔍 验证集:
      损失: 1.5208
      准确率: 0.3275 (32.75%)
      精确率: 0.7995
      召回率: 0.3275
      F1分数: 0.2103
   ⚙️ 其他:
      学习率: 0.000002
      耗时: 13.3秒
   ⏰ 无改善 3/20 (当前: 0.2850, 最佳: 0.7076)

🔍 Confusion Matrix Analysis:
   📊 Training Confusion Matrix Metrics:
      True Positives (TP): 649
      True Negatives (TN): 213
      False Positives (FP): 104
      False Negatives (FN): 182
      Accuracy: 0.7509 (75.09%)
      Precision: 0.8619 (86.19%)
      Recall (Sensitivity): 0.7810 (78.10%)
      Specificity: 0.6719 (67.19%)
      F1-Score: 0.8194
   📊 Validation Confusion Matrix Metrics:
      True Positives (TP): 12
      True Negatives (TN): 82
      False Positives (FP): 0
      False Negatives (FN): 193
      Accuracy: 0.3275 (32.75%)
      Precision: 1.0000 (100.00%)
      Recall (Sensitivity): 0.0585 (5.85%)
      Specificity: 1.0000 (100.00%)
      F1-Score: 0.1106

📈 Train vs Validation Comparison:
   Accuracy Gap: 42.33%
   Precision Gap: 13.81%
   Recall Gap: 72.25%
📊 ROC 曲线已保存到: results/roc_curves.png

============================================================
🚀 EPOCH 22/30 开始训练
   开始时间: 21:25:33
============================================================
🔍 批次 0 数据形状调试:
   原始样本类型: <class 'dict'>
   样本ECG形状: torch.Size([8, 16000])
   样本PCG形状: torch.Size([8, 128, 126])
   提取后ECG形状: torch.Size([8, 1, 16000])
   提取后PCG形状: torch.Size([8, 1, 128, 126])
   提取后标签形状: torch.Size([8])
🔍 开始验证epoch 22...
验证集批次数量: 36
验证数据设备检查:
  ECG设备: cuda:0, 形状: torch.Size([8, 1, 16000])
  PCG设备: cuda:0, 形状: torch.Size([8, 1, 128, 126])
  标签设备: cuda:0, 形状: torch.Size([8])
📊 AUC Score: 0.8355
验证完成: 处理了 287 个样本
🔬 固定学习率: 0.000002 (微调版本)

📊 EPOCH 22 结果:
   🚂 训练集:
      损失: 0.4285
      准确率: 0.7936 (79.36%)
      精确率: 0.8079
      召回率: 0.7936
      F1分数: 0.7985
   🔍 验证集:
      损失: 0.5271
      准确率: 0.7596 (75.96%)
      精确率: 0.8047
      召回率: 0.7596
      F1分数: 0.7698
   ⚙️ 其他:
      学习率: 0.000002
      耗时: 13.3秒
   🎉 Macro F1-Score提升: 0.7359 (新最佳)

🔍 Confusion Matrix Analysis:
   📊 Training Confusion Matrix Metrics:
      True Positives (TP): 683
      True Negatives (TN): 228
      False Positives (FP): 89
      False Negatives (FN): 148
      Accuracy: 0.7936 (79.36%)
      Precision: 0.8847 (88.47%)
      Recall (Sensitivity): 0.8219 (82.19%)
      Specificity: 0.7192 (71.92%)
      F1-Score: 0.8522
   📊 Validation Confusion Matrix Metrics:
      True Positives (TP): 152
      True Negatives (TN): 66
      False Positives (FP): 16
      False Negatives (FN): 53
      Accuracy: 0.7596 (75.96%)
      Precision: 0.9048 (90.48%)
      Recall (Sensitivity): 0.7415 (74.15%)
      Specificity: 0.8049 (80.49%)
      F1-Score: 0.8150

📈 Train vs Validation Comparison:
   Accuracy Gap: 3.40%
   Precision Gap: 2.00%
   Recall Gap: 8.04%
📊 ROC 曲线已保存到: results/roc_curves.png

============================================================
🚀 EPOCH 23/30 开始训练
   开始时间: 21:25:51
============================================================
🔍 批次 0 数据形状调试:
   原始样本类型: <class 'dict'>
   样本ECG形状: torch.Size([8, 16000])
   样本PCG形状: torch.Size([8, 128, 126])
   提取后ECG形状: torch.Size([8, 1, 16000])
   提取后PCG形状: torch.Size([8, 1, 128, 126])
   提取后标签形状: torch.Size([8])
🔍 开始验证epoch 23...
验证集批次数量: 36
验证数据设备检查:
  ECG设备: cuda:0, 形状: torch.Size([8, 1, 16000])
  PCG设备: cuda:0, 形状: torch.Size([8, 1, 128, 126])
  标签设备: cuda:0, 形状: torch.Size([8])
📊 AUC Score: 0.8434
验证完成: 处理了 287 个样本
🔬 固定学习率: 0.000002 (微调版本)

📊 EPOCH 23 结果:
   🚂 训练集:
      损失: 0.3539
      准确率: 0.8171 (81.71%)
      精确率: 0.8313
      召回率: 0.8171
      F1分数: 0.8217
   🔍 验证集:
      损失: 0.5359
      准确率: 0.6969 (69.69%)
      精确率: 0.8051
      召回率: 0.6969
      F1分数: 0.7111
   ⚙️ 其他:
      学习率: 0.000002
      耗时: 12.0秒
   ⏰ 无改善 1/20 (当前: 0.6857, 最佳: 0.7359)

🔍 Confusion Matrix Analysis:
   📊 Training Confusion Matrix Metrics:
      True Positives (TP): 695
      True Negatives (TN): 243
      False Positives (FP): 74
      False Negatives (FN): 136
      Accuracy: 0.8171 (81.71%)
      Precision: 0.9038 (90.38%)
      Recall (Sensitivity): 0.8363 (83.63%)
      Specificity: 0.7666 (76.66%)
      F1-Score: 0.8688
   📊 Validation Confusion Matrix Metrics:
      True Positives (TP): 127
      True Negatives (TN): 73
      False Positives (FP): 9
      False Negatives (FN): 78
      Accuracy: 0.6969 (69.69%)
      Precision: 0.9338 (93.38%)
      Recall (Sensitivity): 0.6195 (61.95%)
      Specificity: 0.8902 (89.02%)
      F1-Score: 0.7449

📈 Train vs Validation Comparison:
   Accuracy Gap: 12.02%
   Precision Gap: 3.01%
   Recall Gap: 21.68%
📊 ROC 曲线已保存到: results/roc_curves.png

============================================================
🚀 EPOCH 24/30 开始训练
   开始时间: 21:26:09
============================================================
🔍 批次 0 数据形状调试:
   原始样本类型: <class 'dict'>
   样本ECG形状: torch.Size([8, 16000])
   样本PCG形状: torch.Size([8, 128, 126])
   提取后ECG形状: torch.Size([8, 1, 16000])
   提取后PCG形状: torch.Size([8, 1, 128, 126])
   提取后标签形状: torch.Size([8])
🔍 开始验证epoch 24...
验证集批次数量: 36
验证数据设备检查:
  ECG设备: cuda:0, 形状: torch.Size([8, 1, 16000])
  PCG设备: cuda:0, 形状: torch.Size([8, 1, 128, 126])
  标签设备: cuda:0, 形状: torch.Size([8])
📊 AUC Score: 0.8446
验证完成: 处理了 287 个样本
🔬 固定学习率: 0.000002 (微调版本)

📊 EPOCH 24 结果:
   🚂 训练集:
      损失: 0.4133
      准确率: 0.7970 (79.70%)
      精确率: 0.8135
      召回率: 0.7970
      F1分数: 0.8025
   🔍 验证集:
      损失: 0.8280
      准确率: 0.5052 (50.52%)
      精确率: 0.7969
      召回率: 0.5052
      F1分数: 0.4928
   ⚙️ 其他:
      学习率: 0.000002
      耗时: 11.6秒
   ⏰ 无改善 2/20 (当前: 0.5039, 最佳: 0.7359)

🔍 Confusion Matrix Analysis:
   📊 Training Confusion Matrix Metrics:
      True Positives (TP): 681
      True Negatives (TN): 234
      False Positives (FP): 83
      False Negatives (FN): 150
      Accuracy: 0.7970 (79.70%)
      Precision: 0.8914 (89.14%)
      Recall (Sensitivity): 0.8195 (81.95%)
      Specificity: 0.7382 (73.82%)
      F1-Score: 0.8539
   📊 Validation Confusion Matrix Metrics:
      True Positives (TP): 65
      True Negatives (TN): 80
      False Positives (FP): 2
      False Negatives (FN): 140
      Accuracy: 0.5052 (50.52%)
      Precision: 0.9701 (97.01%)
      Recall (Sensitivity): 0.3171 (31.71%)
      Specificity: 0.9756 (97.56%)
      F1-Score: 0.4779

📈 Train vs Validation Comparison:
   Accuracy Gap: 29.18%
   Precision Gap: 7.88%
   Recall Gap: 50.24%
📊 ROC 曲线已保存到: results/roc_curves.png

============================================================
🚀 EPOCH 25/30 开始训练
   开始时间: 21:26:25
============================================================
🔍 批次 0 数据形状调试:
   原始样本类型: <class 'dict'>
   样本ECG形状: torch.Size([8, 16000])
   样本PCG形状: torch.Size([8, 128, 126])
   提取后ECG形状: torch.Size([8, 1, 16000])
   提取后PCG形状: torch.Size([8, 1, 128, 126])
   提取后标签形状: torch.Size([8])
🔍 开始验证epoch 25...
验证集批次数量: 36
验证数据设备检查:
  ECG设备: cuda:0, 形状: torch.Size([8, 1, 16000])
  PCG设备: cuda:0, 形状: torch.Size([8, 1, 128, 126])
  标签设备: cuda:0, 形状: torch.Size([8])
📊 AUC Score: 0.8378
验证完成: 处理了 287 个样本
🔬 固定学习率: 0.000002 (微调版本)

📊 EPOCH 25 结果:
   🚂 训练集:
      损失: 0.3591
      准确率: 0.8232 (82.32%)
      精确率: 0.8368
      召回率: 0.8232
      F1分数: 0.8276
   🔍 验证集:
      损失: 0.5503
      准确率: 0.7317 (73.17%)
      精确率: 0.7913
      召回率: 0.7317
      F1分数: 0.7441
   ⚙️ 其他:
      学习率: 0.000002
      耗时: 11.6秒
   ⏰ 无改善 3/20 (当前: 0.7103, 最佳: 0.7359)

🔍 Confusion Matrix Analysis:
   📊 Training Confusion Matrix Metrics:
      True Positives (TP): 699
      True Negatives (TN): 246
      False Positives (FP): 71
      False Negatives (FN): 132
      Accuracy: 0.8232 (82.32%)
      Precision: 0.9078 (90.78%)
      Recall (Sensitivity): 0.8412 (84.12%)
      Specificity: 0.7760 (77.60%)
      F1-Score: 0.8732
   📊 Validation Confusion Matrix Metrics:
      True Positives (TP): 144
      True Negatives (TN): 66
      False Positives (FP): 16
      False Negatives (FN): 61
      Accuracy: 0.7317 (73.17%)
      Precision: 0.9000 (90.00%)
      Recall (Sensitivity): 0.7024 (70.24%)
      Specificity: 0.8049 (80.49%)
      F1-Score: 0.7890

📈 Train vs Validation Comparison:
   Accuracy Gap: 9.15%
   Precision Gap: 0.78%
   Recall Gap: 13.87%
📊 ROC 曲线已保存到: results/roc_curves.png

============================================================
🚀 EPOCH 26/30 开始训练
   开始时间: 21:26:42
============================================================
🔍 批次 0 数据形状调试:
   原始样本类型: <class 'dict'>
   样本ECG形状: torch.Size([8, 16000])
   样本PCG形状: torch.Size([8, 128, 126])
   提取后ECG形状: torch.Size([8, 1, 16000])
   提取后PCG形状: torch.Size([8, 1, 128, 126])
   提取后标签形状: torch.Size([8])
🔍 开始验证epoch 26...
验证集批次数量: 36
验证数据设备检查:
  ECG设备: cuda:0, 形状: torch.Size([8, 1, 16000])
  PCG设备: cuda:0, 形状: torch.Size([8, 1, 128, 126])
  标签设备: cuda:0, 形状: torch.Size([8])
📊 AUC Score: 0.8453
验证完成: 处理了 287 个样本
🔬 固定学习率: 0.000002 (微调版本)

📊 EPOCH 26 结果:
   🚂 训练集:
      损失: 0.3412
      准确率: 0.8171 (81.71%)
      精确率: 0.8324
      召回率: 0.8171
      F1分数: 0.8219
   🔍 验证集:
      损失: 0.9454
      准确率: 0.4983 (49.83%)
      精确率: 0.7849
      召回率: 0.4983
      F1分数: 0.4856
   ⚙️ 其他:
      学习率: 0.000002
      耗时: 11.0秒
   ⏰ 无改善 4/20 (当前: 0.4969, 最佳: 0.7359)

🔍 Confusion Matrix Analysis:
   📊 Training Confusion Matrix Metrics:
      True Positives (TP): 693
      True Negatives (TN): 245
      False Positives (FP): 72
      False Negatives (FN): 138
      Accuracy: 0.8171 (81.71%)
      Precision: 0.9059 (90.59%)
      Recall (Sensitivity): 0.8339 (83.39%)
      Specificity: 0.7729 (77.29%)
      F1-Score: 0.8684
   📊 Validation Confusion Matrix Metrics:
      True Positives (TP): 64
      True Negatives (TN): 79
      False Positives (FP): 3
      False Negatives (FN): 141
      Accuracy: 0.4983 (49.83%)
      Precision: 0.9552 (95.52%)
      Recall (Sensitivity): 0.3122 (31.22%)
      Specificity: 0.9634 (96.34%)
      F1-Score: 0.4706

📈 Train vs Validation Comparison:
   Accuracy Gap: 31.88%
   Precision Gap: 4.93%
   Recall Gap: 52.17%
📊 ROC 曲线已保存到: results/roc_curves.png

============================================================
🚀 EPOCH 27/30 开始训练
   开始时间: 21:26:58
============================================================
🔍 批次 0 数据形状调试:
   原始样本类型: <class 'dict'>
   样本ECG形状: torch.Size([8, 16000])
   样本PCG形状: torch.Size([8, 128, 126])
   提取后ECG形状: torch.Size([8, 1, 16000])
   提取后PCG形状: torch.Size([8, 1, 128, 126])
   提取后标签形状: torch.Size([8])
🔍 开始验证epoch 27...
验证集批次数量: 36
验证数据设备检查:
  ECG设备: cuda:0, 形状: torch.Size([8, 1, 16000])
  PCG设备: cuda:0, 形状: torch.Size([8, 1, 128, 126])
  标签设备: cuda:0, 形状: torch.Size([8])
📊 AUC Score: 0.8160
验证完成: 处理了 287 个样本
🔬 固定学习率: 0.000002 (微调版本)

📊 EPOCH 27 结果:
   🚂 训练集:
      损失: 0.3694
      准确率: 0.8127 (81.27%)
      精确率: 0.8221
      召回率: 0.8127
      F1分数: 0.8162
   🔍 验证集:
      损失: 0.5908
      准确率: 0.7003 (70.03%)
      精确率: 0.7561
      召回率: 0.7003
      F1分数: 0.7138
   ⚙️ 其他:
      学习率: 0.000002
      耗时: 11.3秒
   ⏰ 无改善 5/20 (当前: 0.6744, 最佳: 0.7359)

🔍 Confusion Matrix Analysis:
   📊 Training Confusion Matrix Metrics:
      True Positives (TP): 702
      True Negatives (TN): 231
      False Positives (FP): 86
      False Negatives (FN): 129
      Accuracy: 0.8127 (81.27%)
      Precision: 0.8909 (89.09%)
      Recall (Sensitivity): 0.8448 (84.48%)
      Specificity: 0.7287 (72.87%)
      F1-Score: 0.8672
   📊 Validation Confusion Matrix Metrics:
      True Positives (TP): 141
      True Negatives (TN): 60
      False Positives (FP): 22
      False Negatives (FN): 64
      Accuracy: 0.7003 (70.03%)
      Precision: 0.8650 (86.50%)
      Recall (Sensitivity): 0.6878 (68.78%)
      Specificity: 0.7317 (73.17%)
      F1-Score: 0.7663

📈 Train vs Validation Comparison:
   Accuracy Gap: 11.24%
   Precision Gap: 2.58%
   Recall Gap: 15.70%
📊 ROC 曲线已保存到: results/roc_curves.png

============================================================
🚀 EPOCH 28/30 开始训练
   开始时间: 21:27:14
============================================================
🔍 批次 0 数据形状调试:
   原始样本类型: <class 'dict'>
   样本ECG形状: torch.Size([8, 16000])
   样本PCG形状: torch.Size([8, 128, 126])
   提取后ECG形状: torch.Size([8, 1, 16000])
   提取后PCG形状: torch.Size([8, 1, 128, 126])
   提取后标签形状: torch.Size([8])
🔍 开始验证epoch 28...
验证集批次数量: 36
验证数据设备检查:
  ECG设备: cuda:0, 形状: torch.Size([8, 1, 16000])
  PCG设备: cuda:0, 形状: torch.Size([8, 1, 128, 126])
  标签设备: cuda:0, 形状: torch.Size([8])
📊 AUC Score: 0.8518
验证完成: 处理了 287 个样本
🔬 固定学习率: 0.000002 (微调版本)

📊 EPOCH 28 结果:
   🚂 训练集:
      损失: 0.3750
      准确率: 0.8197 (81.97%)
      精确率: 0.8319
      召回率: 0.8197
      F1分数: 0.8238
   🔍 验证集:
      损失: 0.4862
      准确率: 0.7735 (77.35%)
      精确率: 0.8005
      召回率: 0.7735
      F1分数: 0.7811
   ⚙️ 其他:
      学习率: 0.000002
      耗时: 11.7秒
   🎉 Macro F1-Score提升: 0.7436 (新最佳)

🔍 Confusion Matrix Analysis:
   📊 Training Confusion Matrix Metrics:
      True Positives (TP): 700
      True Negatives (TN): 241
      False Positives (FP): 76
      False Negatives (FN): 131
      Accuracy: 0.8197 (81.97%)
      Precision: 0.9021 (90.21%)
      Recall (Sensitivity): 0.8424 (84.24%)
      Specificity: 0.7603 (76.03%)
      F1-Score: 0.8712
   📊 Validation Confusion Matrix Metrics:
      True Positives (TP): 160
      True Negatives (TN): 62
      False Positives (FP): 20
      False Negatives (FN): 45
      Accuracy: 0.7735 (77.35%)
      Precision: 0.8889 (88.89%)
      Recall (Sensitivity): 0.7805 (78.05%)
      Specificity: 0.7561 (75.61%)
      F1-Score: 0.8312

📈 Train vs Validation Comparison:
   Accuracy Gap: 4.62%
   Precision Gap: 1.32%
   Recall Gap: 6.19%
📊 ROC 曲线已保存到: results/roc_curves.png

============================================================
🚀 EPOCH 29/30 开始训练
   开始时间: 21:27:31
============================================================
🔍 批次 0 数据形状调试:
   原始样本类型: <class 'dict'>
   样本ECG形状: torch.Size([8, 16000])
   样本PCG形状: torch.Size([8, 128, 126])
   提取后ECG形状: torch.Size([8, 1, 16000])
   提取后PCG形状: torch.Size([8, 1, 128, 126])
   提取后标签形状: torch.Size([8])
🔍 开始验证epoch 29...
验证集批次数量: 36
验证数据设备检查:
  ECG设备: cuda:0, 形状: torch.Size([8, 1, 16000])
  PCG设备: cuda:0, 形状: torch.Size([8, 1, 128, 126])
  标签设备: cuda:0, 形状: torch.Size([8])
📊 AUC Score: 0.8670
验证完成: 处理了 287 个样本
🔬 固定学习率: 0.000002 (微调版本)

📊 EPOCH 29 结果:
   🚂 训练集:
      损失: 0.3500
      准确率: 0.8449 (84.49%)
      精确率: 0.8554
      召回率: 0.8449
      F1分数: 0.8483
   🔍 验证集:
      损失: 0.6659
      准确率: 0.5958 (59.58%)
      精确率: 0.7952
      召回率: 0.5958
      F1分数: 0.6047
   ⚙️ 其他:
      学习率: 0.000002
      耗时: 11.8秒
   ⏰ 无改善 1/20 (当前: 0.5944, 最佳: 0.7436)

🔍 Confusion Matrix Analysis:
   📊 Training Confusion Matrix Metrics:
      True Positives (TP): 716
      True Negatives (TN): 254
      False Positives (FP): 63
      False Negatives (FN): 115
      Accuracy: 0.8449 (84.49%)
      Precision: 0.9191 (91.91%)
      Recall (Sensitivity): 0.8616 (86.16%)
      Specificity: 0.8013 (80.13%)
      F1-Score: 0.8894
   📊 Validation Confusion Matrix Metrics:
      True Positives (TP): 94
      True Negatives (TN): 77
      False Positives (FP): 5
      False Negatives (FN): 111
      Accuracy: 0.5958 (59.58%)
      Precision: 0.9495 (94.95%)
      Recall (Sensitivity): 0.4585 (45.85%)
      Specificity: 0.9390 (93.90%)
      F1-Score: 0.6184

📈 Train vs Validation Comparison:
   Accuracy Gap: 24.91%
   Precision Gap: 3.04%
   Recall Gap: 40.31%
📊 ROC 曲线已保存到: results/roc_curves.png

============================================================
🚀 EPOCH 30/30 开始训练
   开始时间: 21:27:48
============================================================
🔍 批次 0 数据形状调试:
   原始样本类型: <class 'dict'>
   样本ECG形状: torch.Size([8, 16000])
   样本PCG形状: torch.Size([8, 128, 126])
   提取后ECG形状: torch.Size([8, 1, 16000])
   提取后PCG形状: torch.Size([8, 1, 128, 126])
   提取后标签形状: torch.Size([8])
🔍 开始验证epoch 30...
验证集批次数量: 36
验证数据设备检查:
  ECG设备: cuda:0, 形状: torch.Size([8, 1, 16000])
  PCG设备: cuda:0, 形状: torch.Size([8, 1, 128, 126])
  标签设备: cuda:0, 形状: torch.Size([8])
📊 AUC Score: 0.8731
验证完成: 处理了 287 个样本
🔬 固定学习率: 0.000002 (微调版本)

📊 EPOCH 30 结果:
   🚂 训练集:
      损失: 0.3162
      准确率: 0.8319 (83.19%)
      精确率: 0.8392
      召回率: 0.8319
      F1分数: 0.8346
   🔍 验证集:
      损失: 0.5192
      准确率: 0.7073 (70.73%)
      精确率: 0.8090
      召回率: 0.7073
      F1分数: 0.7212
   ⚙️ 其他:
      学习率: 0.000002
      耗时: 10.5秒
   ⏰ 无改善 2/20 (当前: 0.6953, 最佳: 0.7436)

🔍 Confusion Matrix Analysis:
   📊 Training Confusion Matrix Metrics:
      True Positives (TP): 716
      True Negatives (TN): 239
      False Positives (FP): 78
      False Negatives (FN): 115
      Accuracy: 0.8319 (83.19%)
      Precision: 0.9018 (90.18%)
      Recall (Sensitivity): 0.8616 (86.16%)
      Specificity: 0.7539 (75.39%)
      F1-Score: 0.8812
   📊 Validation Confusion Matrix Metrics:
      True Positives (TP): 130
      True Negatives (TN): 73
      False Positives (FP): 9
      False Negatives (FN): 75
      Accuracy: 0.7073 (70.73%)
      Precision: 0.9353 (93.53%)
      Recall (Sensitivity): 0.6341 (63.41%)
      Specificity: 0.8902 (89.02%)
      F1-Score: 0.7558

📈 Train vs Validation Comparison:
   Accuracy Gap: 12.46%
   Precision Gap: 3.35%
   Recall Gap: 22.75%
📊 ROC 曲线已保存到: results/roc_curves.png

🎉 训练完成！
   最佳准确率: 0.7875 (78.75%)
   结束时间: 2025-07-02 21:28:04
================================================================================
模型已保存到: ./checkpoints/multimodal_ecg_pcg_best.pth

============================================================
📊 生成最终分析报告...
============================================================
📈 绘制最终 ROC 曲线...
📊 ROC 曲线已保存到: results/roc_curves.png
🎯 最佳 AUC: 0.8731
🎯 最终 AUC: 0.8731

📋 训练摘要:
   总训练轮数: 30
   最佳验证准确率: 0.7875 (78.75%)
   最终验证准确率: 0.7073 (70.73%)
   最佳 AUC 分数: 0.8731
   最终 AUC 分数: 0.8731

📁 生成的文件:
   模型文件: ./checkpoints/multimodal_ecg_pcg_best.pth
   训练曲线: results/realtime_training_curves.png
   混淆矩阵: results/latest_confusion_matrix.png
   ROC 曲线: results/roc_curves.png
   实时 ROC: results/realtime_roc_curve.png

** 🎉 多模态训练完成！最佳模型已保存到: ./checkpoints/multimodal_ecg_pcg_best.pth **

2025-06-20 17:03:20,850 - TorchECG - INFO - training configurations are as follows:
{
    "debug": True,
    "final_model_name": None,
    "log_step": 20,
    "flooding_level": 0.0,
    "early_stopping": {
        "min_delta": 0.001,
        "patience": 10
    },
    "log_dir": "logs",
    "checkpoints": checkpoints,
    "model_dir": None,
    "working_dir": "",
    "prefix": "TorchECG",
    "DTYPE": DTYPE(STR='float32', NP=dtype('float32'), TORCH=torch.float32, INT=32),
    "str_dtype": "float32",
    "np_dtype": float32,
    "dtype": torch.float32,
    "device": cuda,
    "eps": 1e-07,
    "SEED": 42,
    "RNG": Generator(PCG64),
    "RNG_sample": functools.partial(<built-in method choice of numpy.random._generator.Generator object at 0x7fcc6e5f6f20>, replace=False, shuffle=False),
    "RNG_randint": functools.partial(<built-in method integers of numpy.random._generator.Generator object at 0x7fcc6e5f6f20>, endpoint=True),
    "set_seed": <function set_seed at 0x7fcc6e6679d0>,
    "change_dtype": <function change_dtype at 0x7fcc6e5aa160>,
    "classes": [
        "N",
        "A"
    ],
    "n_epochs": 100,
    "batch_size": 8,
    "optimizer": "adamw_amsgrad",
    "momentum": 0.9,
    "betas": [
        0.9, 0.999
    ],
    "decay": 0.01,
    "learning_rate": 0.1,
    "lr": 0.1,
    "lr_scheduler": "one_cycle",
    "lr_step_size": 50,
    "lr_gamma": 0.1,
    "max_lr": 0.002,
    "burn_in": 400,
    "steps": [
        5000, 10000
    ],
    "loss": "AsymmetricLoss",
    "loss_kw": {
        "gamma_pos": 0,
        "gamma_neg": 0.2,
        "implementation": "deep-psp"
    },
    "monitor": "challenge_metric",
    "eval_every": 20,
    "cnn_name": "transformer",
    "rnn_name": "none",
    "attn_name": "none",
    "input_len": 16000,
    "input_len_tol": 3200,
    "sig_slice_tol": 0.4,
    "siglen": 16000,
    "physionetOnly": False
}
2025-06-20 17:03:21,311 - TorchECG - INFO - training configurations are as follows:
{
    "debug": True,
    "final_model_name": None,
    "log_step": 20,
    "flooding_level": 0.0,
    "early_stopping": {
        "min_delta": 0.001,
        "patience": 10
    },
    "log_dir": "logs",
    "checkpoints": checkpoints,
    "model_dir": None,
    "working_dir": "",
    "prefix": "TorchECG",
    "DTYPE": DTYPE(STR='float32', NP=dtype('float32'), TORCH=torch.float32, INT=32),
    "str_dtype": "float32",
    "np_dtype": float32,
    "dtype": torch.float32,
    "device": cuda,
    "eps": 1e-07,
    "SEED": 42,
    "RNG": Generator(PCG64),
    "RNG_sample": functools.partial(<built-in method choice of numpy.random._generator.Generator object at 0x7fcc6e5f6f20>, replace=False, shuffle=False),
    "RNG_randint": functools.partial(<built-in method integers of numpy.random._generator.Generator object at 0x7fcc6e5f6f20>, endpoint=True),
    "set_seed": <function set_seed at 0x7fcc6e6679d0>,
    "change_dtype": <function change_dtype at 0x7fcc6e5aa160>,
    "classes": [
        "N",
        "A"
    ],
    "n_epochs": 100,
    "batch_size": 8,
    "optimizer": "adamw_amsgrad",
    "momentum": 0.9,
    "betas": [
        0.9, 0.999
    ],
    "decay": 0.01,
    "learning_rate": 0.1,
    "lr": 0.1,
    "lr_scheduler": "one_cycle",
    "lr_step_size": 50,
    "lr_gamma": 0.1,
    "max_lr": 0.002,
    "burn_in": 400,
    "steps": [
        5000, 10000
    ],
    "loss": "AsymmetricLoss",
    "loss_kw": {
        "gamma_pos": 0,
        "gamma_neg": 0.2,
        "implementation": "deep-psp"
    },
    "monitor": "challenge_metric",
    "eval_every": 20,
    "cnn_name": "transformer",
    "rnn_name": "none",
    "attn_name": "none",
    "input_len": 16000,
    "input_len_tol": 3200,
    "sig_slice_tol": 0.4,
    "siglen": 16000,
    "physionetOnly": True
}
2025-06-20 17:03:22,120 - TorchECG - INFO - 
Starting training:
------------------
Epochs:          100
Batch size:      8
Learning rate:   0.1
Training size:   1962
Validation size: 841
Device:          cuda
Optimizer:       adamw_amsgrad
Dataset classes: ['N', 'A']
-----------------------------------------

2025-06-20 17:03:22,122 - TorchECG - INFO - Train epoch_0:
--------------------------------------------------------------------------------------------------------------
2025-06-20 17:03:44,836 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 20: train/loss : 0.6430
Epoch 0 / Step 20: train/lr :   0.0001
--------------------------------------------------
2025-06-20 17:03:45,986 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 40: train/loss : 0.4151
Epoch 0 / Step 40: train/lr :   0.0001
--------------------------------------------------
2025-06-20 17:03:46,859 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 60: train/loss : 0.4297
Epoch 0 / Step 60: train/lr :   0.0001
--------------------------------------------------
2025-06-20 17:03:47,544 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 80: train/loss : 0.0994
Epoch 0 / Step 80: train/lr :   0.0001
--------------------------------------------------
2025-06-20 17:03:48,228 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 100: train/loss : 0.8635
Epoch 0 / Step 100: train/lr :   0.0001
--------------------------------------------------
2025-06-20 17:03:48,907 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 120: train/loss : 0.5671
Epoch 0 / Step 120: train/lr :   0.0001
--------------------------------------------------
2025-06-20 17:03:49,579 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 140: train/loss : 0.9257
Epoch 0 / Step 140: train/lr :   0.0001
--------------------------------------------------
2025-06-20 17:03:50,252 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 160: train/loss : 0.2698
Epoch 0 / Step 160: train/lr :   0.0001
--------------------------------------------------
2025-06-20 17:03:50,925 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 180: train/loss : 0.3716
Epoch 0 / Step 180: train/lr :   0.0001
--------------------------------------------------
2025-06-20 17:03:51,601 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 200: train/loss : 0.1223
Epoch 0 / Step 200: train/lr :   0.0001
--------------------------------------------------
2025-06-20 17:03:52,276 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 220: train/loss : 0.4300
Epoch 0 / Step 220: train/lr :   0.0001
--------------------------------------------------
2025-06-20 17:03:52,952 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 240: train/loss : 0.3254
Epoch 0 / Step 240: train/lr :   0.0001
--------------------------------------------------

2023-08-13 23:11:51,049 - TorchECG - INFO - training configurations are as follows:
{
    "debug": True,
    "final_model_name": None,
    "log_step": 20,
    "flooding_level": 0.0,
    "early_stopping": {
        "min_delta": 0.001,
        "patience": 10
    },
    "log_dir": "logs",
    "checkpoints": checkpoints,
    "model_dir": None,
    "working_dir": "",
    "prefix": "TorchECG",
    "DTYPE": DTYPE(STR='float32', NP=dtype('float32'), TORCH=torch.float32, INT=32),
    "str_dtype": "float32",
    "np_dtype": float32,
    "dtype": torch.float32,
    "device": cpu,
    "eps": 1e-07,
    "SEED": 42,
    "RNG": Generator(PCG64),
    "RNG_sample": functools.partial(<built-in method choice of numpy.random._generator.Generator object at 0x2b6ad26ea900>, replace=False, shuffle=False),
    "RNG_randint": functools.partial(<built-in method integers of numpy.random._generator.Generator object at 0x2b6ad26ea900>, endpoint=True),
    "set_seed": <function set_seed at 0x2b6ad26f2a60>,
    "change_dtype": <function change_dtype at 0x2b6ad2738040>,
    "classes": [
        "N",
        "A"
    ],
    "n_epochs": 100,
    "batch_size": 8,
    "optimizer": "adamw_amsgrad",
    "momentum": 0.949,
    "betas": [
        0.9, 0.999
    ],
    "decay": 0.01,
    "learning_rate": 0.0001,
    "lr": 0.0001,
    "lr_scheduler": "one_cycle",
    "lr_step_size": 50,
    "lr_gamma": 0.1,
    "max_lr": 0.002,
    "burn_in": 400,
    "steps": [
        5000, 10000
    ],
    "loss": "AsymmetricLoss",
    "loss_kw": {
        "gamma_pos": 0,
        "gamma_neg": 0.2,
        "implementation": "deep-psp"
    },
    "monitor": "challenge_metric",
    "eval_every": 20,
    "cnn_name": "transformer",
    "rnn_name": "none",
    "attn_name": "none",
    "input_len": 16000,
    "input_len_tol": 3200,
    "sig_slice_tol": 0.4,
    "siglen": 16000,
    "physionetOnly": False
}
2023-08-13 23:11:52,061 - TorchECG - INFO - training configurations are as follows:
{
    "debug": True,
    "final_model_name": None,
    "log_step": 20,
    "flooding_level": 0.0,
    "early_stopping": {
        "min_delta": 0.001,
        "patience": 10
    },
    "log_dir": "logs",
    "checkpoints": checkpoints,
    "model_dir": None,
    "working_dir": "",
    "prefix": "TorchECG",
    "DTYPE": DTYPE(STR='float32', NP=dtype('float32'), TORCH=torch.float32, INT=32),
    "str_dtype": "float32",
    "np_dtype": float32,
    "dtype": torch.float32,
    "device": cpu,
    "eps": 1e-07,
    "SEED": 42,
    "RNG": Generator(PCG64),
    "RNG_sample": functools.partial(<built-in method choice of numpy.random._generator.Generator object at 0x2b6ad26ea900>, replace=False, shuffle=False),
    "RNG_randint": functools.partial(<built-in method integers of numpy.random._generator.Generator object at 0x2b6ad26ea900>, endpoint=True),
    "set_seed": <function set_seed at 0x2b6ad26f2a60>,
    "change_dtype": <function change_dtype at 0x2b6ad2738040>,
    "classes": [
        "N",
        "A"
    ],
    "n_epochs": 100,
    "batch_size": 8,
    "optimizer": "adamw_amsgrad",
    "momentum": 0.949,
    "betas": [
        0.9, 0.999
    ],
    "decay": 0.01,
    "learning_rate": 0.0001,
    "lr": 0.0001,
    "lr_scheduler": "one_cycle",
    "lr_step_size": 50,
    "lr_gamma": 0.1,
    "max_lr": 0.002,
    "burn_in": 400,
    "steps": [
        5000, 10000
    ],
    "loss": "AsymmetricLoss",
    "loss_kw": {
        "gamma_pos": 0,
        "gamma_neg": 0.2,
        "implementation": "deep-psp"
    },
    "monitor": "challenge_metric",
    "eval_every": 20,
    "cnn_name": "transformer",
    "rnn_name": "none",
    "attn_name": "none",
    "input_len": 16000,
    "input_len_tol": 3200,
    "sig_slice_tol": 0.4,
    "siglen": 16000,
    "physionetOnly": True
}
2023-08-13 23:11:53,623 - TorchECG - INFO - 
Starting training:
------------------
Epochs:          100
Batch size:      8
Learning rate:   0.0001
Training size:   1962
Validation size: 841
Device:          cpu
Optimizer:       adamw_amsgrad
Dataset classes: ['N', 'A']
-----------------------------------------

2023-08-13 23:11:53,636 - TorchECG - INFO - Train epoch_0:
--------------------------------------------------------------------------------------------------------------
2023-08-13 23:12:23,607 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 20: train/loss : 0.3169
Epoch 0 / Step 20: train/lr :   0.0001
--------------------------------------------------
2023-08-13 23:12:50,569 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 40: train/loss : 0.5050
Epoch 0 / Step 40: train/lr :   0.0001
--------------------------------------------------
2023-08-13 23:13:17,965 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 60: train/loss : 0.4242
Epoch 0 / Step 60: train/lr :   0.0001
--------------------------------------------------
2023-08-13 23:13:46,052 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 80: train/loss : 0.3474
Epoch 0 / Step 80: train/lr :   0.0001
--------------------------------------------------
2023-08-13 23:14:12,686 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 100: train/loss : 0.2593
Epoch 0 / Step 100: train/lr :   0.0001
--------------------------------------------------
2023-08-13 23:14:39,911 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 120: train/loss : 0.3216
Epoch 0 / Step 120: train/lr :   0.0001
--------------------------------------------------
2023-08-13 23:15:05,304 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 140: train/loss : 0.2201
Epoch 0 / Step 140: train/lr :   0.0001
--------------------------------------------------
2023-08-13 23:15:32,093 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 160: train/loss : 0.3244
Epoch 0 / Step 160: train/lr :   0.0001
--------------------------------------------------
2023-08-13 23:15:58,998 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 180: train/loss : 0.5407
Epoch 0 / Step 180: train/lr :   0.0001
--------------------------------------------------
2023-08-13 23:16:19,457 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 200: train/loss : 0.2384
Epoch 0 / Step 200: train/lr :   0.0001
--------------------------------------------------
2023-08-13 23:16:37,788 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 220: train/loss : 0.2602
Epoch 0 / Step 220: train/lr :   0.0001
--------------------------------------------------
2023-08-13 23:16:56,709 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 240: train/loss : 0.1544
Epoch 0 / Step 240: train/lr :   0.0001
--------------------------------------------------
2023-08-13 23:18:33,655 - TorchECG - DEBUG - all_scalar_preds.shape = (1962, 2), all_labels.shape = (1962, 1)
2023-08-13 23:18:33,657 - TorchECG - INFO - 
----------------------------------------------
scalar prediction:    [0.479, 0.521]
binary prediction:    [1]
labels:               [1]
predicted classes:    [1]
label classes:        [1]
----------------------------------------------

2023-08-13 23:18:33,657 - TorchECG - INFO - 
----------------------------------------------
scalar prediction:    [0.469, 0.531]
binary prediction:    [1]
labels:               [1]
predicted classes:    [1]
label classes:        [1]
----------------------------------------------

2023-08-13 23:18:39,045 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 246: train/auroc :            nan
Epoch 0 / Step 246: train/auprc :            0.2599
Epoch 0 / Step 246: train/accuracy :         0.8481
Epoch 0 / Step 246: train/f_measure :        0.8160
Epoch 0 / Step 246: train/f_beta_measure :   0.8675
Epoch 0 / Step 246: train/g_beta_measure :   0.6430
Epoch 0 / Step 246: train/challenge_metric : 0.9055
--------------------------------------------------

2023-08-13 17:22:16,706 - TorchECG - INFO - training configurations are as follows:
{
    "debug": True,
    "final_model_name": None,
    "log_step": 20,
    "flooding_level": 0.0,
    "early_stopping": {
        "min_delta": 0.001,
        "patience": 10
    },
    "log_dir": "logs",
    "checkpoints": checkpoints,
    "model_dir": None,
    "working_dir": "",
    "prefix": "TorchECG",
    "DTYPE": DTYPE(STR='float32', NP=dtype('float32'), TORCH=torch.float32, INT=32),
    "str_dtype": "float32",
    "np_dtype": float32,
    "dtype": torch.float32,
    "device": cpu,
    "eps": 1e-07,
    "SEED": 42,
    "RNG": Generator(PCG64),
    "RNG_sample": functools.partial(<built-in method choice of numpy.random._generator.Generator object at 0x2ad7d0d989e0>, replace=False, shuffle=False),
    "RNG_randint": functools.partial(<built-in method integers of numpy.random._generator.Generator object at 0x2ad7d0d989e0>, endpoint=True),
    "set_seed": <function set_seed at 0x2ad7d0d9e940>,
    "change_dtype": <function change_dtype at 0x2ad7d0de80d0>,
    "classes": [
        "N",
        "A"
    ],
    "n_epochs": 100,
    "batch_size": 8,
    "optimizer": "adamw_amsgrad",
    "momentum": 0.949,
    "betas": [
        0.9, 0.999
    ],
    "decay": 0.01,
    "learning_rate": 0.0001,
    "lr": 0.0001,
    "lr_scheduler": "one_cycle",
    "lr_step_size": 50,
    "lr_gamma": 0.1,
    "max_lr": 0.002,
    "burn_in": 400,
    "steps": [
        5000, 10000
    ],
    "loss": "AsymmetricLoss",
    "loss_kw": {
        "gamma_pos": 0,
        "gamma_neg": 0.2,
        "implementation": "deep-psp"
    },
    "monitor": "challenge_metric",
    "eval_every": 20,
    "cnn_name": "transformer",
    "rnn_name": "none",
    "attn_name": "none",
    "input_len": 16000,
    "input_len_tol": 3200,
    "sig_slice_tol": 0.4,
    "siglen": 16000,
    "physionetOnly": True
}
2023-08-13 17:22:18,339 - TorchECG - INFO - training configurations are as follows:
{
    "debug": True,
    "final_model_name": None,
    "log_step": 20,
    "flooding_level": 0.0,
    "early_stopping": {
        "min_delta": 0.001,
        "patience": 10
    },
    "log_dir": "logs",
    "checkpoints": checkpoints,
    "model_dir": None,
    "working_dir": "",
    "prefix": "TorchECG",
    "DTYPE": DTYPE(STR='float32', NP=dtype('float32'), TORCH=torch.float32, INT=32),
    "str_dtype": "float32",
    "np_dtype": float32,
    "dtype": torch.float32,
    "device": cpu,
    "eps": 1e-07,
    "SEED": 42,
    "RNG": Generator(PCG64),
    "RNG_sample": functools.partial(<built-in method choice of numpy.random._generator.Generator object at 0x2ad7d0d989e0>, replace=False, shuffle=False),
    "RNG_randint": functools.partial(<built-in method integers of numpy.random._generator.Generator object at 0x2ad7d0d989e0>, endpoint=True),
    "set_seed": <function set_seed at 0x2ad7d0d9e940>,
    "change_dtype": <function change_dtype at 0x2ad7d0de80d0>,
    "classes": [
        "N",
        "A"
    ],
    "n_epochs": 100,
    "batch_size": 8,
    "optimizer": "adamw_amsgrad",
    "momentum": 0.949,
    "betas": [
        0.9, 0.999
    ],
    "decay": 0.01,
    "learning_rate": 0.0001,
    "lr": 0.0001,
    "lr_scheduler": "one_cycle",
    "lr_step_size": 50,
    "lr_gamma": 0.1,
    "max_lr": 0.002,
    "burn_in": 400,
    "steps": [
        5000, 10000
    ],
    "loss": "AsymmetricLoss",
    "loss_kw": {
        "gamma_pos": 0,
        "gamma_neg": 0.2,
        "implementation": "deep-psp"
    },
    "monitor": "challenge_metric",
    "eval_every": 20,
    "cnn_name": "transformer",
    "rnn_name": "none",
    "attn_name": "none",
    "input_len": 16000,
    "input_len_tol": 3200,
    "sig_slice_tol": 0.4,
    "siglen": 16000,
    "physionetOnly": True
}
2023-08-13 17:22:20,003 - TorchECG - INFO - 
Starting training:
------------------
Epochs:          100
Batch size:      8
Learning rate:   0.0001
Training size:   1004
Validation size: 431
Device:          cpu
Optimizer:       adamw_amsgrad
Dataset classes: ['N', 'A']
-----------------------------------------

2023-08-13 17:22:20,008 - TorchECG - INFO - Train epoch_0:
--------------------------------------------------------------------------------------------------------------
2023-08-13 17:22:49,559 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 20: train/loss : 0.6614
Epoch 0 / Step 20: train/lr :   0.0001
--------------------------------------------------
2023-08-13 17:23:15,405 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 40: train/loss : 0.9203
Epoch 0 / Step 40: train/lr :   0.0001
--------------------------------------------------
2023-08-13 17:23:41,681 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 60: train/loss : 0.6970
Epoch 0 / Step 60: train/lr :   0.0001
--------------------------------------------------
2023-08-13 17:24:02,501 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 80: train/loss : 0.7147
Epoch 0 / Step 80: train/lr :   0.0001
--------------------------------------------------
2023-08-13 17:24:28,232 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 100: train/loss : 0.5878
Epoch 0 / Step 100: train/lr :   0.0001
--------------------------------------------------
2023-08-13 17:24:47,696 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 120: train/loss : 0.9712
Epoch 0 / Step 120: train/lr :   0.0001
--------------------------------------------------

2023-08-13 23:23:36,690 - TorchECG - INFO - training configurations are as follows:
{
    "debug": True,
    "final_model_name": None,
    "log_step": 20,
    "flooding_level": 0.0,
    "early_stopping": {
        "min_delta": 0.001,
        "patience": 10
    },
    "log_dir": "logs",
    "checkpoints": checkpoints,
    "model_dir": None,
    "working_dir": "",
    "prefix": "TorchECG",
    "DTYPE": DTYPE(STR='float32', NP=dtype('float32'), TORCH=torch.float32, INT=32),
    "str_dtype": "float32",
    "np_dtype": float32,
    "dtype": torch.float32,
    "device": cpu,
    "eps": 1e-07,
    "SEED": 42,
    "RNG": Generator(PCG64),
    "RNG_sample": functools.partial(<built-in method choice of numpy.random._generator.Generator object at 0x2b9b99bb5900>, replace=False, shuffle=False),
    "RNG_randint": functools.partial(<built-in method integers of numpy.random._generator.Generator object at 0x2b9b99bb5900>, endpoint=True),
    "set_seed": <function set_seed at 0x2b9b99bbda60>,
    "change_dtype": <function change_dtype at 0x2b9b99c04040>,
    "classes": [
        "N",
        "A"
    ],
    "n_epochs": 100,
    "batch_size": 8,
    "optimizer": "adamw_amsgrad",
    "momentum": 0.949,
    "betas": [
        0.9, 0.999
    ],
    "decay": 0.01,
    "learning_rate": 0.0001,
    "lr": 0.0001,
    "lr_scheduler": "one_cycle",
    "lr_step_size": 50,
    "lr_gamma": 0.1,
    "max_lr": 0.002,
    "burn_in": 400,
    "steps": [
        5000, 10000
    ],
    "loss": "AsymmetricLoss",
    "loss_kw": {
        "gamma_pos": 0,
        "gamma_neg": 0.2,
        "implementation": "deep-psp"
    },
    "monitor": "challenge_metric",
    "eval_every": 20,
    "cnn_name": "transformer",
    "rnn_name": "none",
    "attn_name": "none",
    "input_len": 16000,
    "input_len_tol": 3200,
    "sig_slice_tol": 0.4,
    "siglen": 16000,
    "physionetOnly": False
}
2023-08-13 23:23:37,766 - TorchECG - INFO - training configurations are as follows:
{
    "debug": True,
    "final_model_name": None,
    "log_step": 20,
    "flooding_level": 0.0,
    "early_stopping": {
        "min_delta": 0.001,
        "patience": 10
    },
    "log_dir": "logs",
    "checkpoints": checkpoints,
    "model_dir": None,
    "working_dir": "",
    "prefix": "TorchECG",
    "DTYPE": DTYPE(STR='float32', NP=dtype('float32'), TORCH=torch.float32, INT=32),
    "str_dtype": "float32",
    "np_dtype": float32,
    "dtype": torch.float32,
    "device": cpu,
    "eps": 1e-07,
    "SEED": 42,
    "RNG": Generator(PCG64),
    "RNG_sample": functools.partial(<built-in method choice of numpy.random._generator.Generator object at 0x2b9b99bb5900>, replace=False, shuffle=False),
    "RNG_randint": functools.partial(<built-in method integers of numpy.random._generator.Generator object at 0x2b9b99bb5900>, endpoint=True),
    "set_seed": <function set_seed at 0x2b9b99bbda60>,
    "change_dtype": <function change_dtype at 0x2b9b99c04040>,
    "classes": [
        "N",
        "A"
    ],
    "n_epochs": 100,
    "batch_size": 8,
    "optimizer": "adamw_amsgrad",
    "momentum": 0.949,
    "betas": [
        0.9, 0.999
    ],
    "decay": 0.01,
    "learning_rate": 0.0001,
    "lr": 0.0001,
    "lr_scheduler": "one_cycle",
    "lr_step_size": 50,
    "lr_gamma": 0.1,
    "max_lr": 0.002,
    "burn_in": 400,
    "steps": [
        5000, 10000
    ],
    "loss": "AsymmetricLoss",
    "loss_kw": {
        "gamma_pos": 0,
        "gamma_neg": 0.2,
        "implementation": "deep-psp"
    },
    "monitor": "challenge_metric",
    "eval_every": 20,
    "cnn_name": "transformer",
    "rnn_name": "none",
    "attn_name": "none",
    "input_len": 16000,
    "input_len_tol": 3200,
    "sig_slice_tol": 0.4,
    "siglen": 16000,
    "physionetOnly": True
}
2023-08-13 23:23:39,363 - TorchECG - INFO - 
Starting training:
------------------
Epochs:          100
Batch size:      8
Learning rate:   0.0001
Training size:   1962
Validation size: 841
Device:          cpu
Optimizer:       adamw_amsgrad
Dataset classes: ['N', 'A']
-----------------------------------------

2023-08-13 23:23:39,374 - TorchECG - INFO - Train epoch_0:
--------------------------------------------------------------------------------------------------------------
2023-08-13 23:23:48,618 - TorchECG - INFO - training configurations are as follows:
{
    "debug": True,
    "final_model_name": None,
    "log_step": 20,
    "flooding_level": 0.0,
    "early_stopping": {
        "min_delta": 0.001,
        "patience": 10
    },
    "log_dir": "logs",
    "checkpoints": checkpoints,
    "model_dir": None,
    "working_dir": "",
    "prefix": "TorchECG",
    "DTYPE": DTYPE(STR='float32', NP=dtype('float32'), TORCH=torch.float32, INT=32),
    "str_dtype": "float32",
    "np_dtype": float32,
    "dtype": torch.float32,
    "device": cpu,
    "eps": 1e-07,
    "SEED": 42,
    "RNG": Generator(PCG64),
    "RNG_sample": functools.partial(<built-in method choice of numpy.random._generator.Generator object at 0x2b92722e7900>, replace=False, shuffle=False),
    "RNG_randint": functools.partial(<built-in method integers of numpy.random._generator.Generator object at 0x2b92722e7900>, endpoint=True),
    "set_seed": <function set_seed at 0x2b92722efa60>,
    "change_dtype": <function change_dtype at 0x2b9272334040>,
    "classes": [
        "N",
        "A"
    ],
    "n_epochs": 100,
    "batch_size": 8,
    "optimizer": "adamw_amsgrad",
    "momentum": 0.949,
    "betas": [
        0.9, 0.999
    ],
    "decay": 0.01,
    "learning_rate": 0.0001,
    "lr": 0.0001,
    "lr_scheduler": "one_cycle",
    "lr_step_size": 50,
    "lr_gamma": 0.1,
    "max_lr": 0.002,
    "burn_in": 400,
    "steps": [
        5000, 10000
    ],
    "loss": "AsymmetricLoss",
    "loss_kw": {
        "gamma_pos": 0,
        "gamma_neg": 0.2,
        "implementation": "deep-psp"
    },
    "monitor": "challenge_metric",
    "eval_every": 20,
    "cnn_name": "transformer",
    "rnn_name": "none",
    "attn_name": "none",
    "input_len": 16000,
    "input_len_tol": 3200,
    "sig_slice_tol": 0.4,
    "siglen": 16000,
    "physionetOnly": False
}
2023-08-13 23:23:49,572 - TorchECG - INFO - training configurations are as follows:
{
    "debug": True,
    "final_model_name": None,
    "log_step": 20,
    "flooding_level": 0.0,
    "early_stopping": {
        "min_delta": 0.001,
        "patience": 10
    },
    "log_dir": "logs",
    "checkpoints": checkpoints,
    "model_dir": None,
    "working_dir": "",
    "prefix": "TorchECG",
    "DTYPE": DTYPE(STR='float32', NP=dtype('float32'), TORCH=torch.float32, INT=32),
    "str_dtype": "float32",
    "np_dtype": float32,
    "dtype": torch.float32,
    "device": cpu,
    "eps": 1e-07,
    "SEED": 42,
    "RNG": Generator(PCG64),
    "RNG_sample": functools.partial(<built-in method choice of numpy.random._generator.Generator object at 0x2b92722e7900>, replace=False, shuffle=False),
    "RNG_randint": functools.partial(<built-in method integers of numpy.random._generator.Generator object at 0x2b92722e7900>, endpoint=True),
    "set_seed": <function set_seed at 0x2b92722efa60>,
    "change_dtype": <function change_dtype at 0x2b9272334040>,
    "classes": [
        "N",
        "A"
    ],
    "n_epochs": 100,
    "batch_size": 8,
    "optimizer": "adamw_amsgrad",
    "momentum": 0.949,
    "betas": [
        0.9, 0.999
    ],
    "decay": 0.01,
    "learning_rate": 0.0001,
    "lr": 0.0001,
    "lr_scheduler": "one_cycle",
    "lr_step_size": 50,
    "lr_gamma": 0.1,
    "max_lr": 0.002,
    "burn_in": 400,
    "steps": [
        5000, 10000
    ],
    "loss": "AsymmetricLoss",
    "loss_kw": {
        "gamma_pos": 0,
        "gamma_neg": 0.2,
        "implementation": "deep-psp"
    },
    "monitor": "challenge_metric",
    "eval_every": 20,
    "cnn_name": "transformer",
    "rnn_name": "none",
    "attn_name": "none",
    "input_len": 16000,
    "input_len_tol": 3200,
    "sig_slice_tol": 0.4,
    "siglen": 16000,
    "physionetOnly": True
}
2023-08-13 23:23:50,924 - TorchECG - INFO - 
Starting training:
------------------
Epochs:          100
Batch size:      8
Learning rate:   0.0001
Training size:   1962
Validation size: 841
Device:          cpu
Optimizer:       adamw_amsgrad
Dataset classes: ['N', 'A']
-----------------------------------------

2023-08-13 23:23:50,926 - TorchECG - INFO - Train epoch_0:
--------------------------------------------------------------------------------------------------------------
2023-08-13 23:24:20,391 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 20: train/loss : 0.3777
Epoch 0 / Step 20: train/lr :   0.0001
--------------------------------------------------
2023-08-13 23:24:47,332 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 40: train/loss : 0.2005
Epoch 0 / Step 40: train/lr :   0.0001
--------------------------------------------------
2023-08-13 23:25:14,733 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 60: train/loss : 0.5003
Epoch 0 / Step 60: train/lr :   0.0001
--------------------------------------------------
2023-08-13 23:25:41,968 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 80: train/loss : 0.6364
Epoch 0 / Step 80: train/lr :   0.0001
--------------------------------------------------
2023-08-13 23:26:11,119 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 100: train/loss : 0.1519
Epoch 0 / Step 100: train/lr :   0.0001
--------------------------------------------------
2023-08-13 23:26:38,054 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 120: train/loss : 0.6818
Epoch 0 / Step 120: train/lr :   0.0001
--------------------------------------------------
2023-08-13 23:27:05,482 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 140: train/loss : 0.4549
Epoch 0 / Step 140: train/lr :   0.0001
--------------------------------------------------
2023-08-13 23:27:32,460 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 160: train/loss : 0.4069
Epoch 0 / Step 160: train/lr :   0.0001
--------------------------------------------------
2023-08-13 23:27:58,696 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 180: train/loss : 0.1676
Epoch 0 / Step 180: train/lr :   0.0001
--------------------------------------------------
2023-08-13 23:28:20,105 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 200: train/loss : 0.5330
Epoch 0 / Step 200: train/lr :   0.0001
--------------------------------------------------
2023-08-13 23:28:38,772 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 220: train/loss : 0.0376
Epoch 0 / Step 220: train/lr :   0.0001
--------------------------------------------------
2023-08-13 23:28:57,735 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 240: train/loss : 0.2203
Epoch 0 / Step 240: train/lr :   0.0001
--------------------------------------------------

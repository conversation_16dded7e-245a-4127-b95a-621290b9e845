2025-06-20 17:21:34,970 - TorchECG - INFO - training configurations are as follows:
{
    "debug": True,
    "final_model_name": None,
    "log_step": 20,
    "flooding_level": 0.0,
    "early_stopping": {
        "min_delta": 0.001,
        "patience": 10
    },
    "log_dir": "logs",
    "checkpoints": checkpoints,
    "model_dir": None,
    "working_dir": "",
    "prefix": "TorchECG",
    "DTYPE": DTYPE(STR='float32', NP=dtype('float32'), TORCH=torch.float32, INT=32),
    "str_dtype": "float32",
    "np_dtype": float32,
    "dtype": torch.float32,
    "device": cuda,
    "eps": 1e-07,
    "SEED": 42,
    "RNG": Generator(PCG64),
    "RNG_sample": functools.partial(<built-in method choice of numpy.random._generator.Generator object at 0x7fa1d65f5f20>, replace=False, shuffle=False),
    "RNG_randint": functools.partial(<built-in method integers of numpy.random._generator.Generator object at 0x7fa1d65f5f20>, endpoint=True),
    "set_seed": <function set_seed at 0x7fa1d66679d0>,
    "change_dtype": <function change_dtype at 0x7fa1d65aa160>,
    "classes": [
        "N",
        "A"
    ],
    "n_epochs": 100,
    "batch_size": 8,
    "optimizer": "adamw_amsgrad",
    "momentum": 0.9,
    "betas": [
        0.9, 0.999
    ],
    "decay": 0.01,
    "learning_rate": 0.1,
    "lr": 0.1,
    "lr_scheduler": "one_cycle",
    "lr_step_size": 50,
    "lr_gamma": 0.1,
    "max_lr": 0.002,
    "burn_in": 400,
    "steps": [
        5000, 10000
    ],
    "loss": "AsymmetricLoss",
    "loss_kw": {
        "gamma_pos": 0,
        "gamma_neg": 0.2,
        "implementation": "deep-psp"
    },
    "monitor": "challenge_metric",
    "eval_every": 20,
    "cnn_name": "transformer",
    "rnn_name": "none",
    "attn_name": "none",
    "input_len": 16000,
    "input_len_tol": 3200,
    "sig_slice_tol": 0.4,
    "siglen": 16000,
    "physionetOnly": False
}
2025-06-20 17:21:35,416 - TorchECG - INFO - training configurations are as follows:
{
    "debug": True,
    "final_model_name": None,
    "log_step": 20,
    "flooding_level": 0.0,
    "early_stopping": {
        "min_delta": 0.001,
        "patience": 10
    },
    "log_dir": "logs",
    "checkpoints": checkpoints,
    "model_dir": None,
    "working_dir": "",
    "prefix": "TorchECG",
    "DTYPE": DTYPE(STR='float32', NP=dtype('float32'), TORCH=torch.float32, INT=32),
    "str_dtype": "float32",
    "np_dtype": float32,
    "dtype": torch.float32,
    "device": cuda,
    "eps": 1e-07,
    "SEED": 42,
    "RNG": Generator(PCG64),
    "RNG_sample": functools.partial(<built-in method choice of numpy.random._generator.Generator object at 0x7fa1d65f5f20>, replace=False, shuffle=False),
    "RNG_randint": functools.partial(<built-in method integers of numpy.random._generator.Generator object at 0x7fa1d65f5f20>, endpoint=True),
    "set_seed": <function set_seed at 0x7fa1d66679d0>,
    "change_dtype": <function change_dtype at 0x7fa1d65aa160>,
    "classes": [
        "N",
        "A"
    ],
    "n_epochs": 100,
    "batch_size": 8,
    "optimizer": "adamw_amsgrad",
    "momentum": 0.9,
    "betas": [
        0.9, 0.999
    ],
    "decay": 0.01,
    "learning_rate": 0.1,
    "lr": 0.1,
    "lr_scheduler": "one_cycle",
    "lr_step_size": 50,
    "lr_gamma": 0.1,
    "max_lr": 0.002,
    "burn_in": 400,
    "steps": [
        5000, 10000
    ],
    "loss": "AsymmetricLoss",
    "loss_kw": {
        "gamma_pos": 0,
        "gamma_neg": 0.2,
        "implementation": "deep-psp"
    },
    "monitor": "challenge_metric",
    "eval_every": 20,
    "cnn_name": "transformer",
    "rnn_name": "none",
    "attn_name": "none",
    "input_len": 16000,
    "input_len_tol": 3200,
    "sig_slice_tol": 0.4,
    "siglen": 16000,
    "physionetOnly": True
}
2025-06-20 17:21:36,217 - TorchECG - INFO - 
Starting training:
------------------
Epochs:          100
Batch size:      8
Learning rate:   0.1
Training size:   1962
Validation size: 841
Device:          cuda
Optimizer:       adamw_amsgrad
Dataset classes: ['N', 'A']
-----------------------------------------

2025-06-20 17:21:36,218 - TorchECG - INFO - Train epoch_0:
--------------------------------------------------------------------------------------------------------------
2025-06-20 17:21:58,686 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 20: train/loss : 0.3857
Epoch 0 / Step 20: train/lr :   0.0001
--------------------------------------------------
2025-06-20 17:21:59,619 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 40: train/loss : 0.6705
Epoch 0 / Step 40: train/lr :   0.0001
--------------------------------------------------
2025-06-20 17:22:00,510 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 60: train/loss : 0.4048
Epoch 0 / Step 60: train/lr :   0.0001
--------------------------------------------------
2025-06-20 17:22:01,181 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 80: train/loss : 0.6513
Epoch 0 / Step 80: train/lr :   0.0001
--------------------------------------------------
2025-06-20 17:22:01,852 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 100: train/loss : 0.3496
Epoch 0 / Step 100: train/lr :   0.0001
--------------------------------------------------
2025-06-20 17:22:02,521 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 120: train/loss : 0.9078
Epoch 0 / Step 120: train/lr :   0.0001
--------------------------------------------------
2025-06-20 17:22:03,187 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 140: train/loss : 0.2215
Epoch 0 / Step 140: train/lr :   0.0001
--------------------------------------------------
2025-06-20 17:22:03,854 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 160: train/loss : 0.3520
Epoch 0 / Step 160: train/lr :   0.0001
--------------------------------------------------
2025-06-20 17:22:04,522 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 180: train/loss : 0.5129
Epoch 0 / Step 180: train/lr :   0.0001
--------------------------------------------------
2025-06-20 17:22:05,188 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 200: train/loss : 0.4252
Epoch 0 / Step 200: train/lr :   0.0001
--------------------------------------------------
2025-06-20 17:22:05,858 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 220: train/loss : 0.3037
Epoch 0 / Step 220: train/lr :   0.0001
--------------------------------------------------
2025-06-20 17:22:06,527 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 240: train/loss : 0.2804
Epoch 0 / Step 240: train/lr :   0.0001
--------------------------------------------------
2025-06-20 17:22:36,893 - TorchECG - DEBUG - all_scalar_preds.shape = (1962, 2), all_labels.shape = (1962, 1)
2025-06-20 17:22:36,893 - TorchECG - INFO - 
----------------------------------------------
scalar prediction:    [0.574, 0.426]
binary prediction:    [0]
labels:               [1]
predicted classes:    [0]
label classes:        [1]
----------------------------------------------

2025-06-20 17:22:36,894 - TorchECG - INFO - 
----------------------------------------------
scalar prediction:    [0.523, 0.477]
binary prediction:    [0]
labels:               [0]
predicted classes:    [0]
label classes:        [0]
----------------------------------------------

2025-06-20 17:22:39,584 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 246: train/auroc :            nan
Epoch 0 / Step 246: train/auprc :            0.6730
Epoch 0 / Step 246: train/accuracy :         0.1473
Epoch 0 / Step 246: train/f_measure :        0.0510
Epoch 0 / Step 246: train/f_beta_measure :   0.0569
Epoch 0 / Step 246: train/g_beta_measure :   0.0187
Epoch 0 / Step 246: train/challenge_metric : 0.0616
--------------------------------------------------

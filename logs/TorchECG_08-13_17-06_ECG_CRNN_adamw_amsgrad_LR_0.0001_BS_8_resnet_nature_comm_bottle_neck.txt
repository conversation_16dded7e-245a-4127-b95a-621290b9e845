2023-08-13 17:06:26,591 - TorchECG - INFO - training configurations are as follows:
{
    "debug": True,
    "final_model_name": None,
    "log_step": 20,
    "flooding_level": 0.0,
    "early_stopping": {
        "min_delta": 0.001,
        "patience": 10
    },
    "log_dir": "logs",
    "checkpoints": checkpoints,
    "model_dir": None,
    "working_dir": "",
    "prefix": "TorchECG",
    "DTYPE": DTYPE(STR='float32', NP=dtype('float32'), TORCH=torch.float32, INT=32),
    "str_dtype": "float32",
    "np_dtype": float32,
    "dtype": torch.float32,
    "device": cpu,
    "eps": 1e-07,
    "SEED": 42,
    "RNG": Generator(PCG64),
    "RNG_sample": functools.partial(<built-in method choice of numpy.random._generator.Generator object at 0x2b100db52f20>, replace=False, shuffle=False),
    "RNG_randint": functools.partial(<built-in method integers of numpy.random._generator.Generator object at 0x2b100db52f20>, endpoint=True),
    "set_seed": <function set_seed at 0x2b100df1c280>,
    "change_dtype": <function change_dtype at 0x2b100df1c790>,
    "classes": [
        "N",
        "A"
    ],
    "n_epochs": 100,
    "batch_size": 8,
    "optimizer": "adamw_amsgrad",
    "momentum": 0.949,
    "betas": [
        0.9, 0.999
    ],
    "decay": 0.01,
    "learning_rate": 0.0001,
    "lr": 0.0001,
    "lr_scheduler": "one_cycle",
    "lr_step_size": 50,
    "lr_gamma": 0.1,
    "max_lr": 0.002,
    "burn_in": 400,
    "steps": [
        5000, 10000
    ],
    "loss": "AsymmetricLoss",
    "loss_kw": {
        "gamma_pos": 0,
        "gamma_neg": 0.2,
        "implementation": "deep-psp"
    },
    "monitor": "challenge_metric",
    "eval_every": 20,
    "cnn_name": "transformer",
    "rnn_name": "none",
    "attn_name": "none",
    "input_len": 16000,
    "input_len_tol": 3200,
    "sig_slice_tol": 0.4,
    "siglen": 16000,
    "physionetOnly": True
}
2023-08-13 17:06:28,113 - TorchECG - INFO - training configurations are as follows:
{
    "debug": True,
    "final_model_name": None,
    "log_step": 20,
    "flooding_level": 0.0,
    "early_stopping": {
        "min_delta": 0.001,
        "patience": 10
    },
    "log_dir": "logs",
    "checkpoints": checkpoints,
    "model_dir": None,
    "working_dir": "",
    "prefix": "TorchECG",
    "DTYPE": DTYPE(STR='float32', NP=dtype('float32'), TORCH=torch.float32, INT=32),
    "str_dtype": "float32",
    "np_dtype": float32,
    "dtype": torch.float32,
    "device": cpu,
    "eps": 1e-07,
    "SEED": 42,
    "RNG": Generator(PCG64),
    "RNG_sample": functools.partial(<built-in method choice of numpy.random._generator.Generator object at 0x2b100db52f20>, replace=False, shuffle=False),
    "RNG_randint": functools.partial(<built-in method integers of numpy.random._generator.Generator object at 0x2b100db52f20>, endpoint=True),
    "set_seed": <function set_seed at 0x2b100df1c280>,
    "change_dtype": <function change_dtype at 0x2b100df1c790>,
    "classes": [
        "N",
        "A"
    ],
    "n_epochs": 100,
    "batch_size": 8,
    "optimizer": "adamw_amsgrad",
    "momentum": 0.949,
    "betas": [
        0.9, 0.999
    ],
    "decay": 0.01,
    "learning_rate": 0.0001,
    "lr": 0.0001,
    "lr_scheduler": "one_cycle",
    "lr_step_size": 50,
    "lr_gamma": 0.1,
    "max_lr": 0.002,
    "burn_in": 400,
    "steps": [
        5000, 10000
    ],
    "loss": "AsymmetricLoss",
    "loss_kw": {
        "gamma_pos": 0,
        "gamma_neg": 0.2,
        "implementation": "deep-psp"
    },
    "monitor": "challenge_metric",
    "eval_every": 20,
    "cnn_name": "transformer",
    "rnn_name": "none",
    "attn_name": "none",
    "input_len": 16000,
    "input_len_tol": 3200,
    "sig_slice_tol": 0.4,
    "siglen": 16000,
    "physionetOnly": True
}
2023-08-13 17:06:29,754 - TorchECG - INFO - 
Starting training:
------------------
Epochs:          100
Batch size:      8
Learning rate:   0.0001
Training size:   1004
Validation size: 431
Device:          cpu
Optimizer:       adamw_amsgrad
Dataset classes: ['N', 'A']
-----------------------------------------

2023-08-13 17:06:29,758 - TorchECG - INFO - Train epoch_0:
--------------------------------------------------------------------------------------------------------------
2023-08-13 17:06:57,383 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 20: train/loss : 0.6555
Epoch 0 / Step 20: train/lr :   0.0001
--------------------------------------------------
2023-08-13 17:07:22,388 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 40: train/loss : 1.3608
Epoch 0 / Step 40: train/lr :   0.0001
--------------------------------------------------
2023-08-13 17:07:47,078 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 60: train/loss : 0.5957
Epoch 0 / Step 60: train/lr :   0.0001
--------------------------------------------------
2023-08-13 17:08:08,442 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 80: train/loss : 0.2997
Epoch 0 / Step 80: train/lr :   0.0001
--------------------------------------------------
2023-08-13 17:08:27,152 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 100: train/loss : 0.8831
Epoch 0 / Step 100: train/lr :   0.0001
--------------------------------------------------
2023-08-13 17:08:46,074 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 120: train/loss : 0.1990
Epoch 0 / Step 120: train/lr :   0.0001
--------------------------------------------------
2023-08-13 17:09:01,308 - TorchECG - DEBUG - all_scalar_preds.shape = (1004, 1, 16000), all_labels.shape = (1004, 1)

2023-08-14 02:45:21,275 - TorchECG - INFO - training configurations are as follows:
{
    "debug": True,
    "final_model_name": None,
    "log_step": 20,
    "flooding_level": 0.0,
    "early_stopping": {
        "min_delta": 0.001,
        "patience": 10
    },
    "log_dir": "logs",
    "checkpoints": checkpoints,
    "model_dir": None,
    "working_dir": "",
    "prefix": "TorchECG",
    "DTYPE": DTYPE(STR='float32', NP=dtype('float32'), TORCH=torch.float32, INT=32),
    "str_dtype": "float32",
    "np_dtype": float32,
    "dtype": torch.float32,
    "device": cpu,
    "eps": 1e-07,
    "SEED": 42,
    "RNG": Generator(PCG64),
    "RNG_sample": functools.partial(<built-in method choice of numpy.random._generator.Generator object at 0x2b8ebde04900>, replace=False, shuffle=False),
    "RNG_randint": functools.partial(<built-in method integers of numpy.random._generator.Generator object at 0x2b8ebde04900>, endpoint=True),
    "set_seed": <function set_seed at 0x2b8ebde0ca60>,
    "change_dtype": <function change_dtype at 0x2b8ebde52040>,
    "classes": [
        "N",
        "A"
    ],
    "n_epochs": 100,
    "batch_size": 8,
    "optimizer": "adamw_amsgrad",
    "momentum": 0.949,
    "betas": [
        0.9, 0.999
    ],
    "decay": 0.01,
    "learning_rate": 0.0001,
    "lr": 0.0001,
    "lr_scheduler": "one_cycle",
    "lr_step_size": 50,
    "lr_gamma": 0.1,
    "max_lr": 0.002,
    "burn_in": 400,
    "steps": [
        5000, 10000
    ],
    "loss": "AsymmetricLoss",
    "loss_kw": {
        "gamma_pos": 0,
        "gamma_neg": 0.2,
        "implementation": "deep-psp"
    },
    "monitor": "challenge_metric",
    "eval_every": 20,
    "cnn_name": "transformer",
    "rnn_name": "none",
    "attn_name": "none",
    "input_len": 16000,
    "input_len_tol": 3200,
    "sig_slice_tol": 0.4,
    "siglen": 16000,
    "physionetOnly": False
}
2023-08-14 02:45:22,330 - TorchECG - INFO - training configurations are as follows:
{
    "debug": True,
    "final_model_name": None,
    "log_step": 20,
    "flooding_level": 0.0,
    "early_stopping": {
        "min_delta": 0.001,
        "patience": 10
    },
    "log_dir": "logs",
    "checkpoints": checkpoints,
    "model_dir": None,
    "working_dir": "",
    "prefix": "TorchECG",
    "DTYPE": DTYPE(STR='float32', NP=dtype('float32'), TORCH=torch.float32, INT=32),
    "str_dtype": "float32",
    "np_dtype": float32,
    "dtype": torch.float32,
    "device": cpu,
    "eps": 1e-07,
    "SEED": 42,
    "RNG": Generator(PCG64),
    "RNG_sample": functools.partial(<built-in method choice of numpy.random._generator.Generator object at 0x2b8ebde04900>, replace=False, shuffle=False),
    "RNG_randint": functools.partial(<built-in method integers of numpy.random._generator.Generator object at 0x2b8ebde04900>, endpoint=True),
    "set_seed": <function set_seed at 0x2b8ebde0ca60>,
    "change_dtype": <function change_dtype at 0x2b8ebde52040>,
    "classes": [
        "N",
        "A"
    ],
    "n_epochs": 100,
    "batch_size": 8,
    "optimizer": "adamw_amsgrad",
    "momentum": 0.949,
    "betas": [
        0.9, 0.999
    ],
    "decay": 0.01,
    "learning_rate": 0.0001,
    "lr": 0.0001,
    "lr_scheduler": "one_cycle",
    "lr_step_size": 50,
    "lr_gamma": 0.1,
    "max_lr": 0.002,
    "burn_in": 400,
    "steps": [
        5000, 10000
    ],
    "loss": "AsymmetricLoss",
    "loss_kw": {
        "gamma_pos": 0,
        "gamma_neg": 0.2,
        "implementation": "deep-psp"
    },
    "monitor": "challenge_metric",
    "eval_every": 20,
    "cnn_name": "transformer",
    "rnn_name": "none",
    "attn_name": "none",
    "input_len": 16000,
    "input_len_tol": 3200,
    "sig_slice_tol": 0.4,
    "siglen": 16000,
    "physionetOnly": True
}
2023-08-14 02:45:23,922 - TorchECG - INFO - 
Starting training:
------------------
Epochs:          100
Batch size:      8
Learning rate:   0.0001
Training size:   1962
Validation size: 841
Device:          cpu
Optimizer:       adamw_amsgrad
Dataset classes: ['N', 'A']
-----------------------------------------

2023-08-14 02:45:23,926 - TorchECG - INFO - Train epoch_0:
--------------------------------------------------------------------------------------------------------------
2023-08-14 02:45:54,282 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 20: train/loss : 0.1107
Epoch 0 / Step 20: train/lr :   0.0001
--------------------------------------------------
2023-08-14 02:46:21,471 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 40: train/loss : 0.3639
Epoch 0 / Step 40: train/lr :   0.0001
--------------------------------------------------
2023-08-14 02:46:48,108 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 60: train/loss : 0.8002
Epoch 0 / Step 60: train/lr :   0.0001
--------------------------------------------------
2023-08-14 02:47:15,013 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 80: train/loss : 0.4857
Epoch 0 / Step 80: train/lr :   0.0001
--------------------------------------------------
2023-08-14 02:47:42,176 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 100: train/loss : 0.7230
Epoch 0 / Step 100: train/lr :   0.0001
--------------------------------------------------
2023-08-14 02:48:09,299 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 120: train/loss : 0.1912
Epoch 0 / Step 120: train/lr :   0.0001
--------------------------------------------------
2023-08-14 02:48:36,716 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 140: train/loss : 0.3108
Epoch 0 / Step 140: train/lr :   0.0001
--------------------------------------------------
2023-08-14 02:49:04,114 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 160: train/loss : 0.1216
Epoch 0 / Step 160: train/lr :   0.0001
--------------------------------------------------
2023-08-14 02:49:30,560 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 180: train/loss : 0.5947
Epoch 0 / Step 180: train/lr :   0.0001
--------------------------------------------------
2023-08-14 02:49:56,976 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 200: train/loss : 0.1957
Epoch 0 / Step 200: train/lr :   0.0001
--------------------------------------------------
2023-08-14 02:50:16,706 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 220: train/loss : 0.4068
Epoch 0 / Step 220: train/lr :   0.0001
--------------------------------------------------
2023-08-14 02:50:36,591 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 240: train/loss : 0.0735
Epoch 0 / Step 240: train/lr :   0.0001
--------------------------------------------------
2023-08-14 02:52:22,507 - TorchECG - DEBUG - all_scalar_preds.shape = (1962, 2), all_labels.shape = (1962, 1)
2023-08-14 02:52:22,508 - TorchECG - INFO - 
----------------------------------------------
scalar prediction:    [0.553, 0.447]
binary prediction:    [0]
labels:               [0]
predicted classes:    [0]
label classes:        [0]
----------------------------------------------

2023-08-14 02:52:22,508 - TorchECG - INFO - 
----------------------------------------------
scalar prediction:    [0.586, 0.414]
binary prediction:    [0]
labels:               [1]
predicted classes:    [0]
label classes:        [1]
----------------------------------------------

2023-08-14 02:52:27,786 - TorchECG - INFO - Train Metrics:
--------------------------------------------------
Epoch 0 / Step 246: train/auroc :            nan
Epoch 0 / Step 246: train/auprc :            0.6526
Epoch 0 / Step 246: train/accuracy :         0.6279
Epoch 0 / Step 246: train/f_measure :        0.0000
Epoch 0 / Step 246: train/f_beta_measure :   0.0000
Epoch 0 / Step 246: train/g_beta_measure :   0.0000
Epoch 0 / Step 246: train/challenge_metric : 0.0000
--------------------------------------------------
